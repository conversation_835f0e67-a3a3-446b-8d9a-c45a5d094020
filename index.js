// server.js
const express = require('express');
const mongoose = require('mongoose');
const path = require('path');
const morgan = require('morgan');  // Import Morgan for logging
const { auth, admin } = require('./middleware/auth'); // Import your middleware
require('dotenv').config();
// Initialize express app
const app = express();
const cors = require("cors");
// Allow only the frontend to make requests to the API
const corsOptions = {
  origin: '*', // Allow all domains
  methods: ["GET", "POST", "PUT", "DELETE"], // Specify allowed HTTP methods
  credentials: true, // Allow cookies (if needed)
};

// Enable CORS with the specified options
app.use(cors(corsOptions));

// Use Morgan for logging requests automatically
app.use(morgan('dev'));  // 'dev' format is a simple, short log output format

// Middleware to parse incoming JSON requests
app.use(express.json());

// MongoDB connection

mongoose.connect(process.env.MONGO_URI, {
  dbName: 'racineMode', // ✅ forces correct DB

})
.then(() => console.log('Connected to racineMode DB'))
.catch((err) => console.error('MongoDB connection error:', err));


// Use routes
app.use('/api/orders',auth, require('./routes/orders'));
app.use('/api/pieces',auth, require('./routes/pieces'));
app.use('/api/packets',auth, require('./routes/packets'));
app.use('/api/colis',auth, require('./routes/colis')); // Add the new colis route
app.use('/api/clients',auth, require('./routes/clients'));
app.use('/api/articles',auth, require('./routes/articles'));
app.use('/api/operations',auth, require('./routes/operations'));
app.use('/api/gammeDeMontage',auth, require('./routes/gammeDeMontage'));
app.use('/api/stats',auth, require('./routes/stats')); // Add the new stats route
app.use('/api/planning',auth, require('./routes/planningSemaine')); // Add the new planning route
app.use('/api/users', require('./routes/users'));

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
