const express = require('express');
const GammeDeMontage = require('../models/GammeDeMontage');
const Article = require('../models/Article');

const router = express.Router();

// GET all gammes
router.get('/', async (req, res) => {
  try {
    const gammes = await GammeDeMontage.find()
      .populate('operations.operation')
      .sort({ createdAt: -1 });
    res.status(200).json(gammes);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération des gammes', error: err.message });
  }
});

// GET one gamme by ID
router.get('/:id', async (req, res) => {
  try {
    const gamme = await GammeDeMontage.findById(req.params.id)
      .populate('operations.operation');
    if (!gamme) {
      return res.status(404).json({ message: 'Gamme introuvable' });
    }
    res.status(200).json(gamme);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération de la gamme', error: err.message });
  }
});

// CREATE a new gamme and assign it to an article
router.post('/', async (req, res) => {
  try {
    const { tempsMoyenneParPiece, operations, articleId } = req.body;
    console.log("BODY RECEIVED:", req.body); // <-- Add this

    if (typeof tempsMoyenneParPiece !== 'number' || !Array.isArray(operations) || !articleId) {
      return res.status(400).json({ message: 'Champs invalides: tempsMoyenneParPiece, operations et articleId requis' });
    }

    for (const op of operations) {
      if (!op.operation || typeof op.ordre !== 'number') {
        return res.status(400).json({ message: 'Chaque opération doit contenir un ID et un ordre' });
      }
    }

// Ensure the last operation has scanPoint: true
if (operations.length > 0) {
  const lastIndex = operations.length - 1;
  operations[lastIndex].scanPoint = true;
}

    const newGamme = new GammeDeMontage({
      tempsMoyenneParPiece,
      operations,
    });

    await newGamme.save();

    // Update the article with the new gamme reference
    const updatedArticle = await Article.findByIdAndUpdate(
      articleId,
      { gamme: newGamme._id },
      { new: true }
    );

    if (!updatedArticle) {
      return res.status(404).json({ message: 'Article non trouvé pour assigner la gamme' });
    }

    res.status(201).json({
      message: 'Gamme créée et assignée à l\'article',
      gamme: newGamme,
      article: updatedArticle,
    });
  } catch (err) {
    res.status(500).json({
      message: 'Erreur lors de la création de la gamme ou de l\'assignation à l\'article',
      error: err.message
    });
  }
});


// UPDATE a gamme
router.put('/:id', async (req, res) => {
  try {
    const { tempsMoyenneParPiece, operations } = req.body;
    const gamme = await GammeDeMontage.findById(req.params.id);

    if (!gamme) {
      return res.status(404).json({ message: 'Gamme non trouvée' });
    }

    if (tempsMoyenneParPiece !== undefined) gamme.tempsMoyenneParPiece = tempsMoyenneParPiece;
    if (Array.isArray(operations)) gamme.operations = operations;

    await gamme.save();
    res.status(200).json(gamme);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour de la gamme', error: err.message });
  }
});

// DELETE a gamme
router.delete('/:id', async (req, res) => {
  try {
    const gamme = await GammeDeMontage.findById(req.params.id);
    if (!gamme) {
      return res.status(404).json({ message: 'Gamme introuvable' });
    }

    await gamme.deleteOne();
    res.status(200).json({ message: 'Gamme supprimée avec succès' });
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la suppression de la gamme', error: err.message });
  }
});

module.exports = router;
