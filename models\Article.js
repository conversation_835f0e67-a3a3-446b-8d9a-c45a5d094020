// models/Article.js
const mongoose = require('mongoose');

const ArticleSchema = new mongoose.Schema({
  ref: { type: String, required: true, unique: true },
  model: { type: String, required: true },
  gamme: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'GammeDeMontage',
    required: false
  },
  client: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    required: true
  }
}, { timestamps: true });



module.exports = mongoose.models.Article || mongoose.model('Article', ArticleSchema);
