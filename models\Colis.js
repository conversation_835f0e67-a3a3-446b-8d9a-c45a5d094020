// models/Colis.js
const mongoose = require('mongoose');

const ColisSchema = new mongoose.Schema({
  numeroColis: {
    type: Number,
    required: true
  },
  coloris: {
    type: String,
    required: true
  },
  tailles: {
    type: String,
    required: true
  },
  quantite: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    default: "pending", // pending, in_progress, retouche, finnishing, completed
    required: true,
  },
  problems:{
    type: [String],
    default: [],
    required: false
  },
  control: { 
    type: String,
    enum: ['Conforme', 'ANC', 'Bloque'],
    required: false
  },
  // Reference to the parent Order
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Order",
    required: true
  },
  packets: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Packet",
    }
  ],
  qrCode: String,
  scans: [
    {
      type: {
        type: String,
        required: true,
      },
      time: {
        type: Date,
        default: Date.now,
        required: true,
      },
      user: {
        type: String,
        required: false
      }
    }
  ],
}, {
  timestamps: true
});

module.exports = mongoose.models.Colis || mongoose.model('Colis', ColisSchema);
