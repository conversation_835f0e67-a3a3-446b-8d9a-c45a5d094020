const mongoose = require('mongoose');
const bcrypt = require('bcryptjs'); // For password encryption
const jwt = require('jsonwebtoken'); // For JWT token generation
require('dotenv').config();

const UserSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  cin: { type: String, required: true, unique: true }, // National ID (CIN)
  password: { type: String, required: true },
  role: { type: String, default: 'user' }, // Default role is 'user', 'admin' ,responsable_chaine ,  controlleur_fin_chaine , ouvrier_machine , ouvrier_finnition 
});

// Encrypt password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next(); // If password isn't modified, don't re-encrypt

  // Generate salt and hash the password
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Method to compare password during login
UserSchema.methods.comparePassword = function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to generate JWT token
UserSchema.methods.generateAuthToken = function() {
  const payload = { userId: this._id, role: this.role,name:this.name }; // JWT payload contains user ID and role
  const token = jwt.sign(payload, process.env.JWT_SECRET_KEY, { expiresIn: '24h' }); 
  return token;
};

module.exports = mongoose.model('User', UserSchema);
