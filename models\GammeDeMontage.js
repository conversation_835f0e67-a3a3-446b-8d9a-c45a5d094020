// models/GammeDeMontage.js
const mongoose = require('mongoose');

const GammeDeMontageSchema = new mongoose.Schema({
  tempsMoyenneParPiece: { type: Number, required: true }, // In minutes or seconds
  operations: [
    {
      operation: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Operation',
        required: true
      },
      ordre: { type: Number, required: true },
      scanPoint: { type: Boolean, default: false }, // Indicates if this is a scan point
      sousTraitance: { type: Boolean, default: false }, // Indicates if this operation is outsourced
      timeInSeconds: { type: Number, required: true } // Time in seconds

    }
  ]
}, { timestamps: true });

module.exports = mongoose.models.GammeDeMontage || mongoose.model('GammeDeMontage', GammeDeMontageSchema);
