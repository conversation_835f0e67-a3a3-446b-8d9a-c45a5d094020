name: Deploy to Production

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup SSH agent
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.VPS_SSH_KEY }}
        
    - name: Add VPS to known hosts
      run: |
        ssh-keyscan -H ************* >> ~/.ssh/known_hosts
        
    - name: Deploy to VPS
      run: |
        ssh -A -o StrictHostKeyChecking=no ubuntu@************* "
          cd /var/www/racine_mode_tracker_backend &&
          git remote set-url origin https://<EMAIL>/elyes10/racine_mode_tracker_backend.git &&
          git pull origin main &&
          npm install --production &&
          pm2 restart racine-backend || pm2 start ecosystem.config.js --env production
        "