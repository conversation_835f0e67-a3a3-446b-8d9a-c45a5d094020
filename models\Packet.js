// models/Packet.js
const mongoose = require("mongoose");

const PacketSchema = new mongoose.Schema(
  {
    numero: Number,
    size: String,
    status: {
      type: String,
      default: "pending",                      //retouche , finnishing
      required: true,
    },
    bloquer: {
      type: Boolean,
      default: false
    },
    defaut: {
      type: [String], // Array of strings for multiple defauts
      required: false,
    },
    // Reference to the parent Colis
    colis: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Colis",
      required: true
    },
    pieces: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Piece",
      },
    ],
    qrCode: String,
    scans: [
        {
          type: {
            type: String,            //debutGM for first scan only  , nom op de scan + TERMINE    , finGM for end   , ctrlFinCh for controle fin chaine scan , finFinition to end fiition(finition ended)
            required: true,
          },
          time: {
            type: Date,
            default: Date.now, // Automatically sets current time
            required: true,
          },
          user: {
            type: String, // Store userId
            required: false
          }
        }
      ],
  },
  {
    timestamps: true,
  }
);

module.exports =
  mongoose.models.Packet || mongoose.model("Packet", PacketSchema);
