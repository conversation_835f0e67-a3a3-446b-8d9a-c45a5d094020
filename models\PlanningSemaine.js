const mongoose = require('mongoose');

const planningSemaineSchema = new mongoose.Schema({
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  days: [{
    date: {
      type: Date,
      required: true
    },
    dayName: {
      type: String,
      required: true // e.g., "<PERSON><PERSON>", "<PERSON><PERSON>", etc.
    },
    workTime: {
      start: {
        type: String,
        default: "07:30" // Format: "HH:MM"
      },
      end: {
        type: String,
        default: "17:00" // Format: "HH:MM"
      },
      isWorkingDay: {
        type: Boolean,
        default: true
      }
    },
    orders: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Order'
    }]
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
planningSemaineSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual to get week number
planningSemaineSchema.virtual('weekNumber').get(function() {
  const startOfYear = new Date(this.startDate.getFullYear(), 0, 1);
  const pastDaysOfYear = (this.startDate - startOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
});

// Method to initialize days for the week
planningSemaineSchema.methods.initializeDays = function() {
  const days = [];
  const dayNames = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];

  // Default work times for each day (index corresponds to getDay())
  const defaultWorkTimes = [
    { start: "08:00", end: "12:00", isWorkingDay: false }, // Dimanche (Sunday) - non-working day
    { start: "07:30", end: "17:00", isWorkingDay: true },  // Lundi (Monday)
    { start: "07:30", end: "17:00", isWorkingDay: true },  // Mardi (Tuesday)
    { start: "07:30", end: "17:00", isWorkingDay: true },  // Mercredi (Wednesday)
    { start: "07:30", end: "17:00", isWorkingDay: true },  // Jeudi (Thursday)
    { start: "07:30", end: "17:00", isWorkingDay: true },  // Vendredi (Friday)
    { start: "08:00", end: "12:00", isWorkingDay: false }  // Samedi (Saturday) - half day/non-working
  ];

  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(this.startDate);
    currentDate.setDate(currentDate.getDate() + i);
    const dayIndex = currentDate.getDay();

    days.push({
      date: currentDate,
      dayName: dayNames[dayIndex],
      workTime: {
        start: defaultWorkTimes[dayIndex].start,
        end: defaultWorkTimes[dayIndex].end,
        isWorkingDay: defaultWorkTimes[dayIndex].isWorkingDay
      },
      orders: []
    });
  }

  this.days = days;
  return this;
};

module.exports = mongoose.model('PlanningSemaine', planningSemaineSchema);
