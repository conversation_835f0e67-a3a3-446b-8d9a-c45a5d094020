const express = require('express');
const Piece = require('../models/Piece'); // Import Piece model

const router = express.Router();

// GET all pieces
router.get('/', async (req, res) => {
  try {
    const pieces = await Piece.find();
    res.status(200).json(pieces);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch pieces', error: err });
  }
});

// POST a new piece
router.post('/', async (req, res) => {
  try {
    const { numero } = req.body;

    const newPiece = new Piece({ numero });

    await newPiece.save(); // Save the new piece
    res.status(201).json(newPiece);
  } catch (err) {
    res.status(500).json({ message: 'Failed to create piece', error: err });
  }
});

// PUT to update a piece
router.put('/:id', async (req, res) => {
  try {
    const { numero } = req.body;

    const updatedPiece = await Piece.findByIdAndUpdate(
      req.params.id,
      { numero },
      { new: true } // Return the updated piece
    );

    if (!updatedPiece) {
      return res.status(404).json({ message: 'Piece not found' });
    }

    res.status(200).json(updatedPiece);
  } catch (err) {
    res.status(500).json({ message: 'Failed to update piece', error: err });
  }
});

// DELETE a piece
router.delete('/:id', async (req, res) => {
  try {
    const deletedPiece = await Piece.findByIdAndDelete(req.params.id);

    if (!deletedPiece) {
      return res.status(404).json({ message: 'Piece not found' });
    }

    res.status(200).json({ message: 'Piece deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to delete piece', error: err });
  }
});

module.exports = router;
