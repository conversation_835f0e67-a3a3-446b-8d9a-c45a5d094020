// models/Machine.js
const mongoose = require('mongoose');

const MachineSchema = new mongoose.Schema({
  model: { type: String, required: true },
  reference: { type: String, required: true, unique: true },
  dateMiseEnMarche: { type: Date, required: true },
  dateAchat: { type: Date, required: true },
  status: { 
    type: String, 
    enum: ['available', 'in_maintenance', 'assigned', 'broken'], 
    default: 'available',
    required: true 
  },
  assignedWorker: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User',
    default: null 
  }, 
  operations: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Operation' 
  }]
}, { timestamps: true });

module.exports = mongoose.model('Machine', MachineSchema);
