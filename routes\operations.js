const express = require('express');
const Operation = require('../models/Operation');
const GammeDeMontage = require('../models/GammeDeMontage');

const router = express.Router();

// GET all operations
router.get('/', async (req, res) => {
  try {
    const operations = await Operation.find().sort({ createdAt: -1 });
    res.status(200).json(operations);
  } catch (err) {
    res.status(500).json({ message: 'Échec du chargement des opérations', error: err.message });
  }
});

// GET one operation by ID
router.get('/:id', async (req, res) => {
  try {
    const operation = await Operation.findById(req.params.id);
    if (!operation) {
      return res.status(404).json({ message: 'Opération introuvable' });
    }

    res.status(200).json(operation);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération de l\'opération', error: err.message });
  }
});

// CREATE one or many operations
router.post('/', async (req, res) => {
  try {
    const operations = req.body;

    if (!Array.isArray(operations) || operations.length === 0) {
      return res.status(400).json({ message: "Une liste d'opérations est requise." });
    }

    const invalid = operations.find(op => !op.name || typeof op.name !== "string");
    if (invalid) {
      return res.status(400).json({ message: "Chaque opération doit avoir un nom valide." });
    }
    const normalizedOperations = operations.map(op => ({
      ...op,
      name: op.name.trim()
    }));
    const names = normalizedOperations.map(op => op.name);

    // Find existing operations with the same names
    const existingOps = await Operation.find({ name: { $in: names } });
    const existingNames = new Set(existingOps.map(op => op.name));

    // Filter out duplicates
    const toInsert = normalizedOperations.filter(op => !existingNames.has(op.name));

    let inserted = [];
    if (toInsert.length > 0) {
      inserted = await Operation.insertMany(toInsert);
    }

    res.status(201).json({
      message: "Traitement terminé.",
      inserted,
      skipped: operations.filter(op => existingNames.has(op.name)),
    });
  } catch (err) {
    res.status(500).json({
      message: "Erreur lors de la création des opérations.",
      error: err.message,
    });
  }
});



// UPDATE an operation
router.put('/:id', async (req, res) => {
  try {
    const { name } = req.body;

    const operation = await Operation.findById(req.params.id);
    if (!operation) {
      return res.status(404).json({ message: 'Opération introuvable' });
    }

    if (name) operation.name = name;

    await operation.save();
    res.status(200).json(operation);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour de l\'opération', error: err.message });
  }
});

// DELETE an operation
router.delete('/:id', async (req, res) => {
  try {
    // Find the operation by ID
    const operation = await Operation.findById(req.params.id);
    if (!operation) {
      return res.status(404).json({ message: 'Opération introuvable' });
    }

    // Check if the operation is part of any GammeDeMontage
    const gammeExists = await GammeDeMontage.findOne({ 'operations.operation': operation._id });

    if (gammeExists) {
      return res.status(400).json({
        message: 'Cette opération fait partie d\'une Gamme de Montage et ne peut pas être supprimée.'
      });
    }

    // If operation is not part of any GammeDeMontage, proceed with deletion
    await operation.deleteOne();
    res.status(200).json({ message: 'Opération supprimée avec succès' });
  } catch (err) {
    console.log('err del',err.message)
    res.status(500).json({ message: 'Erreur lors de la suppression de l\'opération', error: err.message });
  }
});


module.exports = router;
