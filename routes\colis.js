// routes/colis.js
const express = require('express');
const Colis = require('../models/Colis');
const Packet = require('../models/Packet');
const Piece = require('../models/Piece');
const Order = require('../models/Order');

const router = express.Router();

// GET all colis
router.get('/', async (req, res) => {
  try {
    const colis = await Colis.find()
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .populate('order')
      .populate({
        path: 'packets',
        populate: { path: 'pieces' }
      });

    res.status(200).json(colis);
  } catch (err) {
    console.error('Error fetching all colis:', err);
    res.status(500).json({ message: 'Failed to fetch colis', error: err.message });
  }
});

// GET one colis by ID
router.get('/:id', async (req, res) => {
  try {
    const colis = await Colis.findById(req.params.id)
      .populate('order')
      .populate({
        path: 'packets',
        populate: { path: 'pieces' }
      });

    if (!colis) {
      return res.status(404).json({ message: 'Colis not found' });
    }

    res.status(200).json(colis);
  } catch (err) {
    console.error('Error fetching colis by ID:', err);
    res.status(500).json({ message: 'Failed to fetch colis', error: err.message });
  }
});

// UPDATE a colis by ID
router.put('/:id', async (req, res) => {
  try {
    const { coloris, tailles, quantite, status, problems } = req.body;

    // Find the colis
    const colis = await Colis.findById(req.params.id);
    if (!colis) {
      return res.status(404).json({ message: 'Colis not found' });
    }

    // Update fields if provided
    if (coloris) colis.coloris = coloris;
    if (tailles) colis.tailles = tailles;
    if (quantite) colis.quantite = quantite;
    if (status) colis.status = status;
    if (problems) colis.problems = problems;

    // Save the updated colis
    await colis.save();

    // Return the updated colis with populated fields
    const updatedColis = await Colis.findById(req.params.id)
      .populate('order')
      .populate({
        path: 'packets',
        populate: { path: 'pieces' }
      });

    res.status(200).json(updatedColis);
  } catch (err) {
    console.error('Error updating colis:', err);
    res.status(500).json({ message: 'Failed to update colis', error: err.message });
  }
});

// DELETE a colis by ID
router.delete('/:id', async (req, res) => {
  try {
    // Find the colis and populate its packets and pieces
    const colis = await Colis.findById(req.params.id).populate({
      path: 'packets',
      populate: { path: 'pieces' }
    });

    if (!colis) {
      return res.status(404).json({ message: 'Colis not found' });
    }

    // Find the order that contains this colis
    const order = await Order.findById(colis.order);
    if (order) {
      // Remove the colis from the order's colis array
      order.colis = order.colis.filter(c => c.toString() !== colis._id.toString());
      await order.save();
    }

    // Delete all pieces in each packet
    for (const packet of colis.packets) {
      if (packet.pieces && packet.pieces.length > 0) {
        await Piece.deleteMany({ _id: { $in: packet.pieces } });
      }
    }

    // Delete all packets in this colis
    const packetIds = colis.packets.map(p => p._id);
    await Packet.deleteMany({ _id: { $in: packetIds } });

    // Delete the colis itself
    await Colis.findByIdAndDelete(req.params.id);

    res.status(200).json({ message: 'Colis and all related data deleted successfully' });
  } catch (err) {
    console.error('Error deleting colis:', err);
    res.status(500).json({ message: 'Failed to delete colis', error: err.message });
  }
});

// GET colis by QR code
router.post('/get-by-qr', async (req, res) => {
  try {
    const { qr } = req.body;

    if (!qr) {
      return res.status(400).json({ message: 'QR code is required' });
    }

    // Find the colis using the QR code
    const colis = await Colis.findOne({ qrCode: qr })
      .populate('order')
      .populate({
        path: 'packets',
        populate: { path: 'pieces' }
      });

    if (!colis) {
      return res.status(404).json({ message: 'Colis not found for given QR code' });
    }

    res.status(200).json(colis);
  } catch (err) {
    console.error('Error fetching colis by QR code:', err);
    res.status(500).json({ message: 'Failed to fetch colis', error: err.message });
  }
});

// POST /colis/handle-packet-defects
router.post('/handle-packet-defects', async (req, res) => {
  try {
    const { packetDefects } = req.body;

    if (!packetDefects || !Array.isArray(packetDefects)) {
      return res.status(400).json({ message: 'packetDefects array is required' });
    }

    const results = [];

    // Process each packet defect entry
    for (const packetDefect of packetDefects) {
      const { packetId, defauts } = packetDefect;

      if (!packetId) {
        results.push({
          packetId: null,
          success: false,
          message: 'Packet ID is required'
        });
        continue;
      }

      try {
        // Find the packet and update only the defaut field
        const packet = await Packet.findByIdAndUpdate(
          packetId,
          { defaut: defauts || [] },
          { new: true }
        );

        if (!packet) {
          results.push({
            packetId,
            success: false,
            message: 'Packet not found'
          });
          continue;
        }

        results.push({
          packetId,
          success: true,
          message: 'Packet defauts updated',
          packet
        });

      } catch (error) {
        results.push({
          packetId,
          success: false,
          message: `Error processing packet: ${error.message}`
        });
      }
    }

    res.status(200).json({
      message: 'Packet defects processing completed',
      results
    });

  } catch (err) {
    console.error('Error handling packet defects:', err);
    res.status(500).json({ message: 'Failed to handle packet defects', error: err.message });
  }
});

// POST /colis/handle-colis-defects
router.post('/handle-colis-defects', async (req, res) => {
  try {
    const { colisId, problems, control } = req.body;

    if (!colisId) {
      return res.status(400).json({ message: 'Colis ID is required' });
    }

    if (control && !['Conforme', 'ANC', 'Bloque'].includes(control)) {
      return res.status(400).json({ message: 'Control must be one of: Conforme, ANC, Bloque' });
    }

    // Find the colis and update only the problems and control fields
    const updateFields = {};
    if (problems !== undefined) updateFields.problems = problems || [];
    if (control !== undefined) updateFields.control = control;

    const colis = await Colis.findByIdAndUpdate(
      colisId,
      updateFields,
      { new: true }
    ).populate('order')
    .populate({
      path: 'packets',
      populate: { path: 'pieces' }
    });

    if (!colis) {
      return res.status(404).json({ message: 'Colis non trouver' });
    }

    // If control is 'Bloque', update the order's bloquer field to true
    if (control === 'Bloque') {
      await Order.findByIdAndUpdate(colis.order._id, { bloquer: true });
    }

    res.status(200).json({
      message: 'Colis Controle Terminer',
      colis
    });

  } catch (err) {
    console.error('Error essayer a nouveau:', err);
    res.status(500).json({ message: 'Failed to handle colis defects', error: err.message });
  }
});

module.exports = router;
