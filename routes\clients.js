const express = require('express');
const Client = require('../models/Client');
const Article = require('../models/Article');
const Order = require('../models/Order');
const GammeDeMontage = require('../models/GammeDeMontage');
const router = express.Router();
const Packet = require('../models/Packet');
const Piece = require('../models/Piece');

//Get All clients
router.get('/', async (req, res) => {
    try {
      // Fetch all clients
      const clients = await Client.find().sort({ createdAt: -1 });
  
      // For each client, fetch their articles and orders
      for (let client of clients) {
        // Get articles related to the client
        const articles = await Article.find({ client: client._id });
  
        // Get orders related to each article
        const orders = await Order.find({ article: { $in: articles.map(article => article._id) } });
  
        // Attach articles and orders to the client object
      
        client={
          ...client.toObject(),  // Convert Mongoose client document to plain object
          articles,
          orders
        }
      }
  
      res.status(200).json(clients);
    } catch (err) {
      res.status(500).json({ message: 'Échec du chargement des clients', error: err.message });
    }
  });
  
  

// GET a single client by ID
router.get('/:id', async (req, res) => {
    try {
      // Fetch the client by ID
      const client = await Client.findById(req.params.id);
      if (!client) {
        return res.status(404).json({ message: 'Client introuvable' });
      }
  
      // Get articles related to the client
      const articles = await Article.find({ client: client._id });
  
      // Get orders related to each article
      const orders = await Order.find({ article: { $in: articles.map(article => article._id) } });
  
      // Attach articles and orders to the client object
      client.articles = articles;
      client.orders = orders;
  
      res.status(200).json({
        ...client.toObject(),  // Convert Mongoose client document to plain object
        articles,
        orders
      });
    } catch (err) {
      res.status(500).json({ message: 'Erreur lors de la récupération du client', error: err.message });
    }
  });
  
  

// CREATE a new client
router.post('/', async (req, res) => {
  try {
    const { name, matriculeFiscale, email, phoneNumber } = req.body;

    if (!name || !matriculeFiscale) {
      return res.status(400).json({ message: 'Nom et matricule fiscale sont requis' });
    }

    const existing = await Client.findOne({ matriculeFiscale });
    if (existing) {
      return res.status(400).json({ message: 'Matricule fiscale existe déjà pour un autre client' });
    }

    const client = new Client({ name, matriculeFiscale, email, phoneNumber });
    await client.save();
    res.status(201).json(client);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la création du client', error: err.message });
  }
});

// UPDATE a client
router.put('/:id', async (req, res) => {
  try {
    const { name, matriculeFiscale, email, phoneNumber } = req.body;
    const client = await Client.findById(req.params.id);

    if (!client) {
      return res.status(404).json({ message: 'Client introuvable' });
    }

    if (matriculeFiscale && matriculeFiscale !== client.matriculeFiscale) {
      const existing = await Client.findOne({ matriculeFiscale });
      if (existing) {
        return res.status(400).json({ message: 'Matricule fiscale existe déjà pour un autre client' });
      }
      client.matriculeFiscale = matriculeFiscale;
    }

    if (name) client.name = name;
    if (email) client.email = email;
    if (phoneNumber) client.phoneNumber = phoneNumber;

    await client.save();
    res.status(200).json(client);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour du client', error: err.message });
  }
});


// DELETE a client and cascade delete related articles and gammes
router.delete('/:id', async (req, res) => {
  try {
    const clientId = req.params.id;

    const client = await Client.findById(clientId);
    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Get all articles of the client
    const articles = await Article.find({ client: clientId });

    for (const article of articles) {
      // Delete associated gamme if it exists
      if (article.gamme) {
        await GammeDeMontage.findByIdAndDelete(article.gamme);
      }

      // Find all orders related to the article
      const orders = await Order.find({ article: article._id });

      for (const order of orders) {
        // For each order, delete all packets and their pieces
        const packets = await Packet.find({ _id: { $in: order.packets } });

        for (const packet of packets) {
          // Delete pieces in the packet
          await Piece.deleteMany({ _id: { $in: packet.pieces } });

          // Delete the packet
          await Packet.findByIdAndDelete(packet._id);
        }

        // Delete the order
        await Order.findByIdAndDelete(order._id);
      }

      // Delete the article
      await Article.findByIdAndDelete(article._id);
    }

    // Delete the client
    await Client.findByIdAndDelete(clientId);

    res.status(200).json({ message: 'Client, articles, gammes, orders, packets, and pieces deleted successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Failed to delete client and associated data', error: err.message });
  }
});

module.exports = router;
