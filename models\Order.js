// models/Order.js
const mongoose = require('mongoose');
const Colis = require('./Colis'); // Import the Colis model

const OrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true // Ensure orderNumber is unique
  },
  totalProductionTimeInMinutes: {
    type:Number,
    default:0
  },
  status: { type: String, default: 'pending' }, // Status of the order (pending, in_progress, finnishing, completed, canceled)
  bloquer: { type: Boolean, default: false }, // Indicates if the order is blocked
  launchDate: { type: Date, required: false }, // Date when the order is planned to be launched
  colis: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Colis' }], // References to colis
  qrCode: String, // QR code for the order
  chaine: String, // Production line
  totalPieces: Number,
  scans: [
    {
      type: {
        type: String,
        enum: ['EM', 'SM', 'SF'], // Allowed scan types Entre montage , sortie montage, sortie finition
        required: true,
      },
      time: {
        type: Date,
        default: Date.now, // Automatically sets current time
        required: true,
      },
      user: {
        type: String, // Store userId
        required: false
      }
    }
  ],
  article: {  // Reference to the article associated with the order
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Article',  // This links the order to a specific article
    required: true
  }
}, {
  timestamps: true // Adds createdAt and updatedAt fields
});

module.exports = mongoose.models.Order || mongoose.model('Order', OrderSchema);
