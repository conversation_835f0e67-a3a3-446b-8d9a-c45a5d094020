const express = require('express');
const Article = require('../models/Article');
const Client = require('../models/Client');
const GammeDeMontage = require('../models/GammeDeMontage');
const Order = require('../models/Order');
const Packet = require('../models/Packet');
const Piece = require('../models/Piece');
const router = express.Router();

// GET all articles
router.get('/', async (req, res) => {
  try {
    // Get all articles with populated client and gamme
    const articles = await Article.find()
      .sort({ createdAt: -1 })
      .populate('client')
      .populate('gamme');

    // Create an array to hold the enhanced articles with orders
    const articlesWithOrders = [];

    // For each article, find its related orders
    for (const article of articles) {
      // Find orders related to this article
      const orders = await Order.find({ article: article._id })
        .sort({ createdAt: -1 })
        .populate('article');

      // Convert the Mongoose document to a plain object so we can add the orders
      const articleObj = article.toObject();

      // Add the orders to the article object
      articleObj.orders = orders;

      // Add the enhanced article to our result array
      articlesWithOrders.push(articleObj);
    }

    res.status(200).json(articlesWithOrders);
  } catch (err) {
    res.status(500).json({ message: 'Échec du chargement des articles', error: err.message });
  }
});

// GET one article by ID
router.get('/:id', async (req, res) => {
  try {
    // Find the article by ID and populate client and gamme
    const article = await Article.findById(req.params.id)
      .populate('client')
      .populate({
        path: 'gamme',
        populate: {
          path: 'operations.operation', // 👈 nested population
          model: 'Operation'
        }
      });

    if (!article) {
      return res.status(404).json({ message: 'Article introuvable' });
    }

    // Find orders related to this article
    const orders = await Order.find({ article: article._id })
      .sort({ createdAt: -1 })
      .populate('article');

    // Convert the Mongoose document to a plain object so we can add the orders
    const articleObj = article.toObject();

    // Add the orders to the article object
    articleObj.orders = orders;

    res.status(200).json(articleObj);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la récupération de l\'article', error: err.message });
  }
});

// CREATE an article
router.post('/', async (req, res) => {
  try {
    const { ref, model, gamme, client } = req.body;

    if (!ref || !model || !client) {
      return res.status(400).json({ message: 'Ref, modèle et client sont requis' });
    }

    const existing = await Article.findOne({ ref });
    if (existing) {
      return res.status(400).json({ message: 'Référence d\'article déjà utilisée' });
    }

    const newArticle = new Article({ ref, model, gamme, client });
    await newArticle.save();

    res.status(201).json(newArticle);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la création de l\'article', error: err.message });
  }
});

// UPDATE an article
router.put('/:id', async (req, res) => {
  try {
    const { ref, model, gamme, client } = req.body;

    const article = await Article.findById(req.params.id);
    if (!article) {
      return res.status(404).json({ message: 'Article introuvable' });
    }

    if (ref && ref !== article.ref) {
      const existing = await Article.findOne({ ref });
      if (existing) {
        return res.status(400).json({ message: 'Référence d\'article déjà utilisée' });
      }
      article.ref = ref;
    }

    if (model) article.model = model;
    if (gamme !== undefined) article.gamme = gamme;
    if (client) article.client = client;

    await article.save();
    res.status(200).json(article);
  } catch (err) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour de l\'article', error: err.message });
  }
});

// DELETE an article and its associated gamme
router.delete('/:id', async (req, res) => {
  try {
    const articleId = req.params.id;

    const article = await Article.findById(articleId);
    if (!article) {
      return res.status(404).json({ message: 'Article introuvable' });
    }

    // Delete associated gamme if it exists
    if (article.gamme) {
      await GammeDeMontage.findByIdAndDelete(article.gamme);
    }

    // Find all orders related to the article
    const orders = await Order.find({ article: articleId });

    for (const order of orders) {
      // For each order, find all packets
      const packets = await Packet.find({ _id: { $in: order.packets } });

      for (const packet of packets) {
        // Delete all pieces associated with the packet
        await Piece.deleteMany({ _id: { $in: packet.pieces } });

        // Delete the packet
        await Packet.findByIdAndDelete(packet._id);
      }

      // Delete the order itself
      await Order.findByIdAndDelete(order._id);
    }

    // Finally, delete the article
    await article.deleteOne();

    res.status(200).json({ message: 'Article, gamme, commandes, paquets et pièces supprimés avec succès' });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Erreur lors de la suppression complète de l'article",
      error: err.message,
    });
  }
});


module.exports = router;
