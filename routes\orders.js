// routes/orders.js
const express = require('express');
const Order = require('../models/Order');
const Packet = require('../models/Packet');
const Piece = require('../models/Piece'); // Import Piece model
const Colis = require('../models/Colis'); // Import Colis model

const router = express.Router();

// GET all orders
router.get('/', async (req, res) => {
  try {
    const orders = await Order.find()
      .sort({ createdAt: -1 })
      .populate('article')
      .populate({
        path: 'colis',
        populate: {
          path: 'packets',
          populate: { path: 'pieces' }
        }
      });
    res.status(200).json(orders);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch orders', error: err });
  }
});

// POST a new order
router.post('/', async (req, res) => {
    try {
      const { orderNumber, chaine, totalPieces, colisData, article } = req.body;

      if (!orderNumber || !chaine || !totalPieces || !colisData || !Array.isArray(colisData) || !article) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Validate that colisData is an array with at least one item
      if (colisData.length === 0) {
        return res.status(400).json({ message: "colisData must be a non-empty array" });
      }

      // Sum all pieces from all colis
      const totalPiecesFromColis = colisData.reduce((sum, colis) => sum + (colis.quantite || 0), 0);

      if (totalPiecesFromColis < totalPieces) {
        return res.status(400).json({
          message: `Le nombre total de pièces est inférieur à ${totalPieces}. Il manque ${totalPieces - totalPiecesFromColis} pièces.`,
        });
      }

      // Create the order first
      const newOrder = new Order({
        orderNumber,
        chaine,
        totalPieces,
        colis: [], // Will be populated later
        qrCode: `OF: ${orderNumber}`,
        article,
      });

      await newOrder.save();

      const createdColis = [];

      // Process each colis
      for (const colisItem of colisData) {
        const { numeroColis, coloris, tailles, quantite, piecesPerPacket } = colisItem;

        if (!numeroColis || !coloris || !tailles || !quantite || !piecesPerPacket || piecesPerPacket <= 0) {
          return res.status(400).json({
            message: "Each colis must have numeroColis, coloris, tailles, quantite, and a positive piecesPerPacket value"
          });
        }

        // Create a new colis
        const colis = new Colis({
          numeroColis, // Use the client-provided numeroColis
          coloris,
          tailles,
          quantite,
          order: newOrder._id, // Reference to parent order
          qrCode: `${orderNumber}/C${numeroColis}`,
          packets: [], // Will be populated with packet IDs
        });

        await colis.save();
        createdColis.push(colis._id);

        // Calculate how many packets we need based on piecesPerPacket
        const numPackets = Math.ceil(quantite / piecesPerPacket);
        let packetCounter = 1;
        let remainingPieces = quantite;
        const createdPackets = [];

        // Create packets for this colis
        for (let i = 1; i <= numPackets; i++) {
          const currentPacketSize = Math.min(remainingPieces, piecesPerPacket);

          // Create pieces for this packet
          const createdPieces = [];
          for (let j = 1; j <= currentPacketSize; j++) {
            const piece = new Piece({ numero: j });
            await piece.save();
            createdPieces.push(piece._id);
          }

          // Create the packet
          const packet = new Packet({
            numero: packetCounter++,
            size: tailles, // Use the tailles from colis
            colis: colis._id, // Reference to parent colis
            pieces: createdPieces,
            qrCode: `${orderNumber}/C${numeroColis}/P${packetCounter-1}`,
          });

          await packet.save();
          createdPackets.push(packet._id);
          remainingPieces -= currentPacketSize;
        }

        // Update colis with its packets
        colis.packets = createdPackets;
        await colis.save();
      }

      // Update order with its colis
      newOrder.colis = createdColis;
      await newOrder.save();

      // Return the order with populated colis and packets
      const populatedOrder = await Order.findById(newOrder._id)
        .populate({
          path: 'colis',
          populate: {
            path: 'packets',
            populate: { path: 'pieces' }
          }
        })
        .populate('article');

      res.status(201).json(populatedOrder);
    } catch (err) {
        console.error("Error creating order:", err);

        if (err.code === 11000) {
          return res.status(400).json({
            code: 11000,
            message: "Il existe déjà un ordre de fabrication avec le même numéro.",
          });
        }

        res.status(500).json({
          message: "Échec de la création de l'ordre.",
          error: err.message,
        });
      }
  });





// PUT to update an existing order
router.put('/:id', async (req, res) => {
    try {
      const orderId = req.params.id;
      const { status, chaine, scan, bloquer } = req.body; // Expect scan to be { type: 'EM' | 'SM' | 'SF' }

      const order = await Order.findById(orderId);
      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Optional updates
      if (status) order.status = status;
      if (chaine) order.chaine = chaine;
      if (bloquer !== undefined) order.bloquer = bloquer; // Update bloquer field if provided

      // If a scan is provided, validate and push it
      if (scan && scan.type && ['EM', 'SM', 'SF'].includes(scan.type)) {
        order.scans.push({ type: scan.type, time: new Date() });
      }

      await order.save();
      res.status(200).json(order);
    } catch (err) {
      res.status(500).json({ message: 'Failed to update order', error: err });
    }
  });

  // GET one order by ID
router.get('/:id', async (req, res) => {
    try {
      const order = await Order.findById(req.params.id)
        .populate({
          path: 'colis',
          populate: {
            path: 'packets',
            populate: { path: 'pieces' } // This also populates the pieces inside each packet
          }
        })
        .populate('article'); // 👈 populate article here;

      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      res.status(200).json(order);
    } catch (err) {
      res.status(500).json({ message: 'Failed to fetch order', error: err.message });
    }
  });



// POST /orders/scan to add a scan entry using QR code
router.post('/scan', async (req, res) => {
  try {
    const { type, qr } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user

    if (!['EM', 'SM', 'SF'].includes(type)) {
      return res.status(400).json({ message: 'Invalid scan type' });
    }

    if (!qr) {
      return res.status(400).json({ message: 'QR code is required' });
    }

    const order = await Order.findOne({ qrCode: qr });
    if (!order) {
      return res.status(404).json({ message: 'OF non trouvée pour le code QR donné' });
    }

    // Check if the order is blocked
    if (order.bloquer) {
      return res.status(400).json({ message: 'L\'ordre est bloqué' });
    }

    // Check if order is planned for today (only for EM scan - order launch)
    if (type === 'EM') {
      if (!order.launchDate) {
        return res.status(400).json({ message: 'Ordre n\'est pas dans le planning, contacter l\'administration' });
      }

     
    }

    if (order.status === 'completed') {
      return res.status(200).json({ message: 'OF est déjà terminé', order });
    }

    // Check if the user has already scanned this order with the same type
    const userAlreadyScanned = order.scans.some(scan =>
      scan.type === type && scan.user === userId
    );

    if (userAlreadyScanned) {
      return res.status(400).json({ message: 'Vous avez déjà scanné cet OF' });
    }

    // If type is EM and status already in_progress, do nothing
    if (type === 'EM' && order.status === 'in_progress') {
      return res.status(200).json({ message: 'OF déjà en cours', order });
    }

    // Add scan entry with user information
    order.scans.push({
      type,
      time: new Date(),
      user: userId // Add userId to the scan
    });

    // Update status if type is EM
    if (type === 'EM') {
      order.status = 'in_progress';
    }

    await order.save();

    res.status(200).json({ message: 'OF lancé avec succès', order });
  } catch (err) {
    console.log(err.message)
    res.status(500).json({ message: 'Scan échouer, essayer à nouveau​', error: err.message });
  }
});






  // DELETE an order by ID
router.delete('/:id', async (req, res) => {
    try {
      const order = await Order.findById(req.params.id).populate({
        path: 'colis',
        populate: {
          path: 'packets',
          populate: { path: 'pieces' }
        }
      });

      if (!order) {
        return res.status(404).json({ message: 'Order not found' });
      }

      // Delete all pieces, packets, and colis
      for (const colis of order.colis) {
        for (const packet of colis.packets) {
          if (packet.pieces && packet.pieces.length > 0) {
            await Piece.deleteMany({ _id: { $in: packet.pieces } });
          }
        }

        // Delete all packets in this colis
        const packetIds = colis.packets.map(p => p._id);
        await Packet.deleteMany({ _id: { $in: packetIds } });
      }

      // Delete all colis
      const colisIds = order.colis.map(c => c._id);
      await Colis.deleteMany({ _id: { $in: colisIds } });

      // Delete the order itself
      await Order.findByIdAndDelete(req.params.id);

      res.status(200).json({ message: 'Order and all related data deleted successfully' });
    } catch (err) {
      res.status(500).json({ message: 'Failed to delete order', error: err.message });
    }
  });






// POST /orders/scan-finition-done
router.post('/scan-finition-done', async (req, res) => {
  try {
    const { qr } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user

    if (!qr) {
      return res.status(400).json({ message: 'QR code is required' });
    }

    // Find the order using the QR code
    const order = await Order.findOne({ qrCode: qr }).populate({
      path: 'colis',
      populate: {
        path: 'packets',
        populate: { path: 'pieces' }
      }
    });

    if (!order) {
      return res.status(404).json({ message: 'Order not found for given QR code' });
    }

    // Check if the order is blocked
    if (order.bloquer) {
      return res.status(400).json({ message: 'L\'ordre est bloqué' });
    }

    // Check if the order is already completed
    if (order.status === 'completed') {
      return res.status(200).json({ message: 'Order is already completed', order });
    }

    // Check if the order is in "finnishing" status
    if (order.status !== 'finnishing') {
      return res.status(400).json({
        message: 'La commande doit être en statut « finition » pour marquer la finition comme terminée.',
        currentStatus: order.status
      });
    }

    // Check if the user has already scanned this order with SF type
    const userAlreadyScanned = order.scans.some(scan =>
      scan.type === 'SF' && scan.user === userId
    );

    if (userAlreadyScanned) {
      return res.status(400).json({ message: 'Order déjà scanné par cet utilisateur' });
    }

    // Add a scan entry with type "SF" (Sortie Finition)
    order.scans.push({
      type: 'SF',
      time: new Date(),
      user: userId // Add userId to the scan
    });

    // Update order status to "completed"
    order.status = 'completed';

    // Update all colis, packets, and pieces to "completed" status
    for (const colis of order.colis) {
      // Update colis status to "completed"
      colis.status = 'completed';
      await colis.save();

      // Update all non-blocked packets in this colis
      for (const packet of colis.packets) {
        // Skip blocked packets - they are ignored in completion logic
        if (packet.bloquer) continue;

        // Update packet status to "completed"
        packet.status = 'completed';

        // Add a finFinition scan to the packet
        packet.scans.push({
          type: 'finFinition',
          time: new Date(),
          user: userId // Add userId to the scan
        });

        await packet.save();

        // Update all pieces in the packet to "completed" status
        for (const piece of packet.pieces) {
          await Piece.findByIdAndUpdate(piece._id, {
            status: 'completed'
          });
        }
      }
    }

    await order.save();

    res.status(200).json({
      message: 'Finition terminée',
      order
    });
  } catch (err) {
    console.error('Scan finition done error:', err);
    res.status(500).json({ message: 'Failed to process scan', error: err.message });
  }
});

// Helper function to generate QR code (kept for future use)
// function generateQRCode(data) {
//   return `https://api.qrserver.com/v1/create-qr-code/?data=${data}&size=200x200`;
// }

// GET order tracking details - comprehensive order progress information
router.get('/:id/tracking', async (req, res) => {
  try {
    const orderId = req.params.id;

    // Get order with all related data
    const order = await Order.findById(orderId)
      .populate('article')
      .populate({
        path: 'colis',
        populate: {
          path: 'packets',
          populate: { path: 'pieces' }
        }
      });

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    // Extract key scan times from order
    const orderStartTime = order.scans.find(scan => scan.type === 'EM')?.time || null;
    const orderFinMontage = order.scans.find(scan => scan.type === 'SM')?.time || null;
    const orderFinished = order.scans.find(scan => scan.type === 'SF')?.time || null;

    // Collect all packets from all colis
    const allPackets = [];
    order.colis.forEach(colis => {
      colis.packets.forEach(packet => {
        allPackets.push(packet);
      });
    });

    // Calculate packet status totals
    const packetTotals = {
      enCours: allPackets.filter(p => p.status === 'in_progress').length,
      enAttente: allPackets.filter(p => p.status === 'pending').length,
      done: allPackets.filter(p => p.status === 'completed').length,
      retouche: allPackets.filter(p => p.status === 'retouche').length,
      finnishing: allPackets.filter(p => p.status === 'finnishing').length,
      total: allPackets.length
    };

    // Collect all debutGM and finGM scans with timestamps and piece counts
    const debutGMScans = [];
    const finGMScans = [];

    allPackets.forEach(packet => {
      packet.scans.forEach(scan => {
        if (scan.type === 'debutGM') {
          debutGMScans.push({
            time: scan.time,
            packetId: packet._id,
            user: scan.user,
            pieceCount: packet.pieces.length // Add piece count
          });
        } else if (scan.type === 'finGM') {
          finGMScans.push({
            time: scan.time,
            packetId: packet._id,
            user: scan.user,
            pieceCount: packet.pieces.length // Add piece count
          });
        }
      });
    });

    // Function to group scans by date and hour
    const groupScansByDateAndHour = (scans) => {
      const dailyData = {};

      scans.forEach(scan => {
        const scanDate = new Date(scan.time);
        const dateKey = `${scanDate.getFullYear()}-${String(scanDate.getMonth() + 1).padStart(2, '0')}-${String(scanDate.getDate()).padStart(2, '0')}`;
        const hourKey = `${dateKey} ${String(scanDate.getHours()).padStart(2, '0')}:00`;

        // Initialize date if not exists
        if (!dailyData[dateKey]) {
          dailyData[dateKey] = {
            date: dateKey,
            totalCount: 0,
            totalPieces: 0, // Add total pieces for the day
            hours: {},
            scans: []
          };
        }

        // Initialize hour if not exists
        if (!dailyData[dateKey].hours[hourKey]) {
          dailyData[dateKey].hours[hourKey] = {
            hour: hourKey,
            count: 0,
            totalPieces: 0, // Add total pieces for the hour
            scans: []
          };
        }

        // Add scan to both date and hour
        dailyData[dateKey].totalCount++;
        dailyData[dateKey].scans.push(scan);
        dailyData[dateKey].hours[hourKey].count++;
        dailyData[dateKey].hours[hourKey].scans.push(scan);

        // Add piece counting
        if (scan.pieceCount) {
          dailyData[dateKey].totalPieces += scan.pieceCount;
          dailyData[dateKey].hours[hourKey].totalPieces += scan.pieceCount;
        }
      });

      // Convert hours object to array for each date and sort
      Object.keys(dailyData).forEach(dateKey => {
        dailyData[dateKey].hours = Object.values(dailyData[dateKey].hours)
          .sort((a, b) => new Date(a.hour) - new Date(b.hour));
      });

      // Convert to array and sort by date
      return Object.values(dailyData).sort((a, b) => new Date(a.date) - new Date(b.date));
    };

    // Group scans by date and hour
    const debutGMByDate = groupScansByDateAndHour(debutGMScans);
    const finGMByDate = groupScansByDateAndHour(finGMScans);

    // Calculate order duration phases
    let montagePhase = null;
    let finitionPhase = null;
    let totalDuration = null;

    if (orderStartTime && orderFinMontage) {
      montagePhase = {
        start: orderStartTime,
        end: orderFinMontage,
        durationMinutes: Math.round((new Date(orderFinMontage) - new Date(orderStartTime)) / (1000 * 60))
      };
    }

    if (orderFinMontage && orderFinished) {
      finitionPhase = {
        start: orderFinMontage,
        end: orderFinished,
        durationMinutes: Math.round((new Date(orderFinished) - new Date(orderFinMontage)) / (1000 * 60))
      };
    }

    if (orderStartTime && orderFinished) {
      totalDuration = {
        start: orderStartTime,
        end: orderFinished,
        durationMinutes: Math.round((new Date(orderFinished) - new Date(orderStartTime)) / (1000 * 60))
      };
    }

    // Calculate productivity metrics
    const totalPacketsScanned = debutGMScans.length;
    const totalPacketsCompleted = finGMScans.length;
    const completionRate = totalPacketsScanned > 0 ? ((totalPacketsCompleted / totalPacketsScanned) * 100).toFixed(2) : 0;

    // Calculate piece-based metrics
    const totalPiecesScanned = debutGMScans.reduce((sum, scan) => sum + (scan.pieceCount || 0), 0);
    const totalPiecesCompleted = finGMScans.reduce((sum, scan) => sum + (scan.pieceCount || 0), 0);
    const pieceCompletionRate = totalPiecesScanned > 0 ? ((totalPiecesCompleted / totalPiecesScanned) * 100).toFixed(2) : 0;

    // Build response
    const trackingData = {
      order: {
        id: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        bloquer: order.bloquer,
        launchDate: order.launchDate,
        article: order.article
      },
      timeline: {
        orderStartTime,      // EM scan
        orderFinMontage,     // SM scan
        orderFinished,       // SF scan
        montagePhase,
        finitionPhase,
        totalDuration
      },
      packets: {
        totals: packetTotals,
        scannedCount: {
          debutGM: totalPacketsScanned,
          finGM: totalPacketsCompleted
        },
        pieceCount: {
          debutGM: totalPiecesScanned,
          finGM: totalPiecesCompleted
        },
        productivity: {
          completionRate: parseFloat(completionRate),
          pieceCompletionRate: parseFloat(pieceCompletionRate),
          averageTimePerPacket: montagePhase && totalPacketsCompleted > 0
            ? Math.round(montagePhase.durationMinutes / totalPacketsCompleted)
            : null,
          averageTimePerPiece: montagePhase && totalPiecesCompleted > 0
            ? Math.round(montagePhase.durationMinutes / totalPiecesCompleted)
            : null
        }
      },
      dailyActivity: {
        debutGM: debutGMByDate,
        finGM: finGMByDate,
        summary: {
          totalDaysActive: Math.max(debutGMByDate.length, finGMByDate.length),
          peakDayDebutGM: debutGMByDate.length > 0
            ? debutGMByDate.reduce((max, day) => day.totalCount > max.totalCount ? day : max)
            : null,
          peakDayFinGM: finGMByDate.length > 0
            ? finGMByDate.reduce((max, day) => day.totalCount > max.totalCount ? day : max)
            : null,
          peakDayDebutGMByPieces: debutGMByDate.length > 0
            ? debutGMByDate.reduce((max, day) => day.totalPieces > max.totalPieces ? day : max)
            : null,
          peakDayFinGMByPieces: finGMByDate.length > 0
            ? finGMByDate.reduce((max, day) => day.totalPieces > max.totalPieces ? day : max)
            : null,
          totalDebutGMDays: debutGMByDate.length,
          totalFinGMDays: finGMByDate.length,
          totalPiecesAllDays: {
            debutGM: debutGMByDate.reduce((sum, day) => sum + day.totalPieces, 0),
            finGM: finGMByDate.reduce((sum, day) => sum + day.totalPieces, 0)
          }
        }
      },
      scanDetails: {
        totalDebutGMScans: debutGMScans.length,
        totalFinGMScans: finGMScans.length,
        firstDebutGM: debutGMScans.length > 0
          ? debutGMScans.sort((a, b) => new Date(a.time) - new Date(b.time))[0].time
          : null,
        lastFinGM: finGMScans.length > 0
          ? finGMScans.sort((a, b) => new Date(b.time) - new Date(a.time))[0].time
          : null
      }
    };

    res.status(200).json(trackingData);
  } catch (err) {
    console.error('Error fetching order tracking:', err);
    res.status(500).json({ message: 'Failed to fetch order tracking data', error: err.message });
  }
});

module.exports = router;
