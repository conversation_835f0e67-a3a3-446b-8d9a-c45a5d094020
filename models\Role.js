// models/Role.js
const mongoose = require('mongoose');

const RoleSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true }, // e.g., admin, hr, worker
  permissions: [{ type: String, required: true }], // e.g., ["create_user", "view_clients", "scan_packets"]      link it to Users model and populate permission in auth middleware
}, { timestamps: true });

module.exports = mongoose.model('Role', RoleSchema);
