const express = require('express');
const PlanningSemaine = require('../models/PlanningSemaine');
const Order = require('../models/Order');

const router = express.Router();

// GET available orders for planning (orders without launchDate) - MUST BE BEFORE /:id route
router.get('/available-orders', async (req, res) => {
  try {
    const { status } = req.query;

    let filter = {
      launchDate: { $exists: false },
      bloquer: { $ne: true } // Exclude blocked orders
    };

    if (status) {
      filter.status = status;
    }

    const orders = await Order.find(filter)
      .populate('article')
      .sort({ createdAt: -1 });

    res.status(200).json(orders);
  } catch (err) {
    console.error('Error fetching available orders:', err);
    res.status(500).json({ message: 'Failed to fetch available orders', error: err.message });
  }
});

// POST create planning for current week - MUST BE BEFORE /:id route
router.post('/create-current-week', async (req, res) => {
  try {
    const now = new Date();
    const startOfWeek = new Date(now);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    // Check if planning already exists for current week
    const existingPlanning = await PlanningSemaine.findOne({
      startDate: { $lte: startOfWeek },
      endDate: { $gte: endOfWeek }
    });

    if (existingPlanning) {
      return res.status(400).json({
        message: 'Planning already exists for current week',
        planning: existingPlanning
      });
    }

    // Create new planning for current week
    const planning = new PlanningSemaine({
      startDate: startOfWeek,
      endDate: endOfWeek
    });

    planning.initializeDays();
    await planning.save();

    res.status(201).json({
      message: 'Current week planning created successfully',
      planning
    });
  } catch (err) {
    console.error('Error creating current week planning:', err);
    res.status(500).json({ message: 'Failed to create current week planning', error: err.message });
  }
});

// GET planning by order ID - find planning semaine that contains the order's launch date
router.get('/by-order/:orderId', async (req, res) => {
  try {
    console.log('🔍 Route /by-order/:orderId hit with orderId:', req.params.orderId);
    const { orderId } = req.params;

    // Find the order and check if it has a launch date
    console.log('🔍 Looking for order with ID:', orderId);
    const order = await Order.findById(orderId);
    console.log('🔍 Order found:', order ? 'Yes' : 'No');

    if (!order) {
      console.log('❌ Order not found');
      return res.status(404).json({ message: 'Order not found' });
    }

    console.log('🔍 Order launch date:', order.launchDate);

    // Check if order has a launch date
    if (!order.launchDate) {
      console.log('❌ Order has no launch date');
      return res.status(404).json({ message: 'Planning not found' });
    }

    // Find the planning semaine that contains this launch date
    console.log('🔍 Looking for planning containing date:', order.launchDate);
    const planning = await PlanningSemaine.findOne({
      startDate: { $lte: order.launchDate },
      endDate: { $gte: order.launchDate }
    }).populate({
      path: 'days.orders',
      populate: { path: 'article' }
    });

    console.log('🔍 Planning found:', planning ? 'Yes' : 'No');

    if (!planning) {
      console.log('❌ No planning found for this launch date');
      return res.status(404).json({ message: 'Planning not found' });
    }

    console.log('✅ Returning planning');
    res.status(200).json(planning);
  } catch (err) {
    console.error('❌ Error fetching planning by order:', err);
    res.status(500).json({ message: 'Failed to fetch planning by order', error: err.message });
  }
});

// GET all planning weeks
router.get('/', async (req, res) => {
  try {
    const { year, month } = req.query;

    let filter = {};
    if (year) {
      const startOfYear = new Date(year, 0, 1);
      const endOfYear = new Date(year, 11, 31);
      filter.startDate = { $gte: startOfYear, $lte: endOfYear };
    }
    if (month && year) {
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0);
      filter.startDate = { $gte: startOfMonth, $lte: endOfMonth };
    }

    const plannings = await PlanningSemaine.find(filter)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      })
      .sort({ startDate: -1 });

    res.status(200).json(plannings);
  } catch (err) {
    console.error('Error fetching planning weeks:', err);
    res.status(500).json({ message: 'Failed to fetch planning weeks', error: err.message });
  }
});

// GET planning statistics - MUST BE BEFORE /:id route
router.get('/stats/overview', async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const thisWeekStart = new Date(today);
    const day = thisWeekStart.getDay();
    const diff = thisWeekStart.getDate() - day + (day === 0 ? -6 : 1);
    thisWeekStart.setDate(diff);

    const thisWeekEnd = new Date(thisWeekStart);
    thisWeekEnd.setDate(thisWeekStart.getDate() + 6);

    // Get orders planned for today
    const ordersToday = await Order.find({
      launchDate: {
        $gte: today,
        $lt: tomorrow
      }
    }).populate('article');

    // Get orders planned for this week
    const ordersThisWeek = await Order.find({
      launchDate: {
        $gte: thisWeekStart,
        $lte: thisWeekEnd
      }
    }).populate('article');

    // Get all orders with planning
    const allPlannedOrders = await Order.find({
      launchDate: { $exists: true }
    });

    // Get orders without planning
    const unplannedOrders = await Order.find({
      launchDate: { $exists: false },
      bloquer: { $ne: true },
      status: { $in: ['pending', 'in_progress'] }
    });

    // Calculate statistics
    const stats = {
      today: {
        totalOrders: ordersToday.length,
        totalPieces: ordersToday.reduce((sum, order) => sum + (order.totalPieces || 0), 0),
        estimatedTime: ordersToday.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0),
        ordersByStatus: {
          pending: ordersToday.filter(o => o.status === 'pending').length,
          in_progress: ordersToday.filter(o => o.status === 'in_progress').length,
          finnishing: ordersToday.filter(o => o.status === 'finnishing').length,
          completed: ordersToday.filter(o => o.status === 'completed').length
        },
        orders: ordersToday
      },
      thisWeek: {
        totalOrders: ordersThisWeek.length,
        totalPieces: ordersThisWeek.reduce((sum, order) => sum + (order.totalPieces || 0), 0),
        estimatedTime: ordersThisWeek.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0),
        dailyBreakdown: []
      },
      overall: {
        totalPlannedOrders: allPlannedOrders.length,
        totalUnplannedOrders: unplannedOrders.length,
        planningCoverage: allPlannedOrders.length + unplannedOrders.length > 0
          ? ((allPlannedOrders.length / (allPlannedOrders.length + unplannedOrders.length)) * 100).toFixed(2)
          : 0
      }
    };

    // Calculate daily breakdown for this week
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(thisWeekStart);
      currentDate.setDate(thisWeekStart.getDate() + i);

      const nextDate = new Date(currentDate);
      nextDate.setDate(currentDate.getDate() + 1);

      const dayOrders = ordersThisWeek.filter(order => {
        const orderDate = new Date(order.launchDate);
        return orderDate >= currentDate && orderDate < nextDate;
      });

      const dayNames = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];

      stats.thisWeek.dailyBreakdown.push({
        date: currentDate,
        dayName: dayNames[currentDate.getDay()],
        ordersCount: dayOrders.length,
        totalPieces: dayOrders.reduce((sum, order) => sum + (order.totalPieces || 0), 0),
        estimatedTime: dayOrders.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0),
        isToday: currentDate.toDateString() === today.toDateString()
      });
    }

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error fetching planning overview stats:', err);
    res.status(500).json({ message: 'Failed to fetch planning overview statistics', error: err.message });
  }
});

// GET planning efficiency statistics
router.get('/stats/efficiency', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    // Get orders with launch dates in the specified period
    const orders = await Order.find({
      launchDate: { $gte: startDate }
    }).populate('article');

    const efficiencyStats = {
      onTimeExecution: {
        total: 0,
        onTime: 0,
        early: 0,
        late: 0,
        percentage: 0
      },
      dailyPlanning: {},
      averageOrdersPerDay: 0,
      peakDays: [],
      lowDays: []
    };

    const dailyCounts = {};

    orders.forEach(order => {
      const launchDate = new Date(order.launchDate);
      const dateKey = launchDate.toISOString().split('T')[0];

      // Count orders per day
      dailyCounts[dateKey] = (dailyCounts[dateKey] || 0) + 1;

      // Check execution timing (if order has EM scan)
      const emScan = order.scans.find(s => s.type === 'EM');
      if (emScan) {
        efficiencyStats.onTimeExecution.total++;

        const scanDate = new Date(emScan.time);
        scanDate.setHours(0, 0, 0, 0);
        launchDate.setHours(0, 0, 0, 0);

        if (scanDate.getTime() === launchDate.getTime()) {
          efficiencyStats.onTimeExecution.onTime++;
        } else if (scanDate < launchDate) {
          efficiencyStats.onTimeExecution.early++;
        } else {
          efficiencyStats.onTimeExecution.late++;
        }
      }
    });

    // Calculate on-time percentage
    if (efficiencyStats.onTimeExecution.total > 0) {
      efficiencyStats.onTimeExecution.percentage =
        ((efficiencyStats.onTimeExecution.onTime / efficiencyStats.onTimeExecution.total) * 100).toFixed(2);
    }

    // Process daily planning data
    const dailyValues = Object.values(dailyCounts);
    efficiencyStats.averageOrdersPerDay = dailyValues.length > 0
      ? (dailyValues.reduce((sum, count) => sum + count, 0) / dailyValues.length).toFixed(2)
      : 0;

    // Convert daily counts to array and sort
    const dailyPlanningArray = Object.entries(dailyCounts).map(([date, count]) => ({
      date,
      ordersCount: count
    })).sort((a, b) => b.ordersCount - a.ordersCount);

    efficiencyStats.dailyPlanning = dailyCounts;
    efficiencyStats.peakDays = dailyPlanningArray.slice(0, 5); // Top 5 busiest days
    efficiencyStats.lowDays = dailyPlanningArray.slice(-5).reverse(); // 5 least busy days

    res.status(200).json(efficiencyStats);
  } catch (err) {
    console.error('Error fetching planning efficiency stats:', err);
    res.status(500).json({ message: 'Failed to fetch planning efficiency statistics', error: err.message });
  }
});

// GET unplanned orders statistics
router.get('/stats/unplanned', async (req, res) => {
  try {
    const unplannedOrders = await Order.find({
      launchDate: { $exists: false },
      bloquer: { $ne: true }
    }).populate('article');

    const stats = {
      total: unplannedOrders.length,
      byStatus: {
        pending: unplannedOrders.filter(o => o.status === 'pending').length,
        in_progress: unplannedOrders.filter(o => o.status === 'in_progress').length,
        finnishing: unplannedOrders.filter(o => o.status === 'finnishing').length,
        completed: unplannedOrders.filter(o => o.status === 'completed').length
      },
      byArticle: {},
      totalPieces: unplannedOrders.reduce((sum, order) => sum + (order.totalPieces || 0), 0),
      estimatedTime: unplannedOrders.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0),
      oldestOrder: null,
      newestOrder: null,
      orders: unplannedOrders.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    };

    // Group by article
    unplannedOrders.forEach(order => {
      const articleName = order.article?.name || 'Unknown';
      if (!stats.byArticle[articleName]) {
        stats.byArticle[articleName] = {
          count: 0,
          totalPieces: 0,
          estimatedTime: 0
        };
      }
      stats.byArticle[articleName].count++;
      stats.byArticle[articleName].totalPieces += order.totalPieces || 0;
      stats.byArticle[articleName].estimatedTime += order.totalProductionTimeInMinutes || 0;
    });

    // Find oldest and newest unplanned orders
    if (unplannedOrders.length > 0) {
      const sortedByDate = [...unplannedOrders].sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      stats.oldestOrder = sortedByDate[0];
      stats.newestOrder = sortedByDate[sortedByDate.length - 1];
    }

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error fetching unplanned orders stats:', err);
    res.status(500).json({ message: 'Failed to fetch unplanned orders statistics', error: err.message });
  }
});
// GET work time templates - predefined work schedules
router.get('/work-time-templates', async (req, res) => {
  try {
    const templates = {
      standard: {
        name: "Standard (7:30-16:30)",
        description: "Horaires standard de travail",
        schedule: [
          
          { day: "Lundi", start: "07:30", end: "16:30", isWorkingDay: true },
          { day: "Mardi", start: "07:30", end: "16:30", isWorkingDay: true },
          { day: "Mercredi", start: "07:30", end: "16:30", isWorkingDay: true },
          { day: "Jeudi", start: "07:30", end: "16:30", isWorkingDay: true },
          { day: "Vendredi", start: "07:30", end: "16:30", isWorkingDay: true },
          { day: "Samedi", start: "07:30", end: "13:00", isWorkingDay: true },
          { day: "Dimanche", start: "08:00", end: "12:00", isWorkingDay: false },
        ]
      },
      extended: {
        name: "Horaires étendus (7:30-17:00)",
        description: "Horaires étendus pour production intensive",
       schedule: [
          { day: "Lundi", start: "07:30", end: "17:00", isWorkingDay: true },
          { day: "Mardi", start: "07:30", end: "17:00", isWorkingDay: true },
          { day: "Mercredi", start: "07:30", end: "17:00", isWorkingDay: true },
          { day: "Jeudi", start: "07:30", end: "17:00", isWorkingDay: true },
          { day: "Vendredi", start: "07:30", end: "17:00", isWorkingDay: true },
          { day: "Samedi", start: "07:30", end: "13:00", isWorkingDay: true },
          { day: "Dimanche", start: "08:00", end: "17:00", isWorkingDay: false },

        ]
      },
      reduced: {
        name: "Horaire d'été (7:30-15:30)",
        description: "Horaires d'été",
        schedule: [
          { day: "Lundi", start: "7:30", end: "15:30", isWorkingDay: true },
          { day: "Mardi", start: "7:30", end: "15:30", isWorkingDay: true },
          { day: "Mercredi", start: "7:30", end: "15:30", isWorkingDay: true },
          { day: "Jeudi", start: "7:30", end: "15:30", isWorkingDay: true },
          { day: "Vendredi", start: "7:30", end: "15:30", isWorkingDay: true },
          { day: "Samedi", start: "7:30", end: "13:00", isWorkingDay: true },
           { day: "Dimanche", start: "7:30", end: "12:00", isWorkingDay: false },
        ]
      }
    };

    res.status(200).json(templates);
  } catch (err) {
    console.error('Error fetching work time templates:', err);
    res.status(500).json({ message: 'Failed to fetch work time templates', error: err.message });
  }
});

// GET planning workload distribution
router.get('/stats/workload', async (req, res) => {
  try {
    const { weeks = 4 } = req.query;

    // Get planning data for the specified number of weeks
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (parseInt(weeks) * 7));

    const plannings = await PlanningSemaine.find({
      startDate: { $gte: startDate }
    }).populate({
      path: 'days.orders',
      populate: { path: 'article' }
    }).sort({ startDate: 1 });

    const workloadStats = {
      weeklyBreakdown: [],
      averageOrdersPerWeek: 0,
      averagePiecesPerWeek: 0,
      averageTimePerWeek: 0,
      peakWeek: null,
      lightestWeek: null,
      dailyAverages: {
        Lundi: { orders: 0, pieces: 0, time: 0 },
        Mardi: { orders: 0, pieces: 0, time: 0 },
        Mercredi: { orders: 0, pieces: 0, time: 0 },
        Jeudi: { orders: 0, pieces: 0, time: 0 },
        Vendredi: { orders: 0, pieces: 0, time: 0 },
        Samedi: { orders: 0, pieces: 0, time: 0 },
        Dimanche: { orders: 0, pieces: 0, time: 0 }
      }
    };

    let totalWeeklyOrders = 0;
    let totalWeeklyPieces = 0;
    let totalWeeklyTime = 0;

    plannings.forEach(planning => {
      const weekData = {
        weekStart: planning.startDate,
        weekEnd: planning.endDate,
        totalOrders: 0,
        totalPieces: 0,
        totalTime: 0,
        dailyBreakdown: []
      };

      planning.days.forEach(day => {
        const dayData = {
          dayName: day.dayName,
          date: day.date,
          orders: day.orders.length,
          pieces: day.orders.reduce((sum, order) => sum + (order.totalPieces || 0), 0),
          time: day.orders.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0)
        };

        weekData.totalOrders += dayData.orders;
        weekData.totalPieces += dayData.pieces;
        weekData.totalTime += dayData.time;
        weekData.dailyBreakdown.push(dayData);

        // Add to daily averages
        workloadStats.dailyAverages[day.dayName].orders += dayData.orders;
        workloadStats.dailyAverages[day.dayName].pieces += dayData.pieces;
        workloadStats.dailyAverages[day.dayName].time += dayData.time;
      });

      totalWeeklyOrders += weekData.totalOrders;
      totalWeeklyPieces += weekData.totalPieces;
      totalWeeklyTime += weekData.totalTime;

      workloadStats.weeklyBreakdown.push(weekData);
    });

    // Calculate averages
    const weekCount = plannings.length;
    if (weekCount > 0) {
      workloadStats.averageOrdersPerWeek = (totalWeeklyOrders / weekCount).toFixed(2);
      workloadStats.averagePiecesPerWeek = (totalWeeklyPieces / weekCount).toFixed(2);
      workloadStats.averageTimePerWeek = (totalWeeklyTime / weekCount).toFixed(2);

      // Calculate daily averages
      Object.keys(workloadStats.dailyAverages).forEach(day => {
        workloadStats.dailyAverages[day].orders = (workloadStats.dailyAverages[day].orders / weekCount).toFixed(2);
        workloadStats.dailyAverages[day].pieces = (workloadStats.dailyAverages[day].pieces / weekCount).toFixed(2);
        workloadStats.dailyAverages[day].time = (workloadStats.dailyAverages[day].time / weekCount).toFixed(2);
      });

      // Find peak and lightest weeks
      const sortedWeeks = [...workloadStats.weeklyBreakdown].sort((a, b) => b.totalOrders - a.totalOrders);
      workloadStats.peakWeek = sortedWeeks[0];
      workloadStats.lightestWeek = sortedWeeks[sortedWeeks.length - 1];
    }

    res.status(200).json(workloadStats);
  } catch (err) {
    console.error('Error fetching workload stats:', err);
    res.status(500).json({ message: 'Failed to fetch workload statistics', error: err.message });
  }
});

// GET planning by ID
router.get('/:id', async (req, res) => {
  try {
    const planning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    res.status(200).json(planning);
  } catch (err) {
    console.error('Error fetching planning:', err);
    res.status(500).json({ message: 'Failed to fetch planning', error: err.message });
  }
});

// POST create new planning week
router.post('/', async (req, res) => {
  try {
    const { startDate, endDate } = req.body;

    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }

    // Check if planning already exists for this week
    const existingPlanning = await PlanningSemaine.findOne({
      startDate: new Date(startDate),
      endDate: new Date(endDate)
    });

    if (existingPlanning) {
      return res.status(400).json({ message: 'Planning already exists for this week' });
    }

    // Create new planning
    const planning = new PlanningSemaine({
      startDate: new Date(startDate),
      endDate: new Date(endDate)
    });

    // Initialize days for the week
    planning.initializeDays();

    await planning.save();

    res.status(201).json({
      message: 'Planning week created successfully',
      planning
    });
  } catch (err) {
    console.error('Error creating planning:', err);
    res.status(500).json({ message: 'Failed to create planning', error: err.message });
  }
});

// PUT update planning
router.put('/:id', async (req, res) => {
  try {
    const { startDate, endDate } = req.body;

    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    if (startDate) planning.startDate = new Date(startDate);
    if (endDate) planning.endDate = new Date(endDate);

    // Re-initialize days if dates changed
    if (startDate || endDate) {
      planning.initializeDays();
    }

    await planning.save();

    const updatedPlanning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    res.status(200).json({
      message: 'Planning updated successfully',
      planning: updatedPlanning
    });
  } catch (err) {
    console.error('Error updating planning:', err);
    res.status(500).json({ message: 'Failed to update planning', error: err.message });
  }
});

// DELETE planning
router.delete('/:id', async (req, res) => {
  try {
    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    // Remove launchDate from all orders in this planning
    for (const day of planning.days) {
      if (day.orders.length > 0) {
        await Order.updateMany(
          { _id: { $in: day.orders } },
          { $unset: { launchDate: 1 } }
        );
      }
    }

    await PlanningSemaine.findByIdAndDelete(req.params.id);

    res.status(200).json({ message: 'Planning deleted successfully' });
  } catch (err) {
    console.error('Error deleting planning:', err);
    res.status(500).json({ message: 'Failed to delete planning', error: err.message });
  }
});

// POST assign orders to a specific day
router.post('/:id/assign-orders', async (req, res) => {
  try {
    const { dayIndex, orderIds } = req.body;

    if (dayIndex === undefined || !Array.isArray(orderIds)) {
      return res.status(400).json({ message: 'Day index and order IDs array are required' });
    }

    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    if (dayIndex < 0 || dayIndex >= planning.days.length) {
      return res.status(400).json({ message: 'Invalid day index' });
    }

    // Verify all orders exist
    const orders = await Order.find({ _id: { $in: orderIds } });
    if (orders.length !== orderIds.length) {
      return res.status(400).json({ message: 'Some orders not found' });
    }

    // Remove orders from other days in this planning first
    planning.days.forEach((day, index) => {
      if (index !== dayIndex) {
        day.orders = day.orders.filter(orderId =>
          !orderIds.includes(orderId.toString())
        );
      }
    });

    // Add orders to the specified day (avoid duplicates)
    const currentOrderIds = planning.days[dayIndex].orders.map(id => id.toString());
    orderIds.forEach(orderId => {
      if (!currentOrderIds.includes(orderId)) {
        planning.days[dayIndex].orders.push(orderId);
      }
    });

    await planning.save();

    // Update launchDate for all assigned orders
    const launchDate = planning.days[dayIndex].date;
    await Order.updateMany(
      { _id: { $in: orderIds } },
      { launchDate: launchDate }
    );

    const updatedPlanning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    res.status(200).json({
      message: 'Orders assigned successfully',
      planning: updatedPlanning
    });
  } catch (err) {
    console.error('Error assigning orders:', err);
    res.status(500).json({ message: 'Failed to assign orders', error: err.message });
  }
});

// POST remove orders from a specific day
router.post('/:id/remove-orders', async (req, res) => {
  try {
    const { dayIndex, orderIds } = req.body;

    if (dayIndex === undefined || !Array.isArray(orderIds)) {
      return res.status(400).json({ message: 'Day index and order IDs array are required' });
    }

    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    if (dayIndex < 0 || dayIndex >= planning.days.length) {
      return res.status(400).json({ message: 'Invalid day index' });
    }

    // Remove orders from the specified day
    planning.days[dayIndex].orders = planning.days[dayIndex].orders.filter(orderId =>
      !orderIds.includes(orderId.toString())
    );

    await planning.save();

    // Remove launchDate from the orders
    await Order.updateMany(
      { _id: { $in: orderIds } },
      { $unset: { launchDate: 1 } }
    );

    const updatedPlanning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    res.status(200).json({
      message: 'Orders removed successfully',
      planning: updatedPlanning
    });
  } catch (err) {
    console.error('Error removing orders:', err);
    res.status(500).json({ message: 'Failed to remove orders', error: err.message });
  }
});

// POST move order from one day to another within the same planning
router.post('/:id/move-order', async (req, res) => {
  try {
    const { orderId, fromDayIndex, toDayIndex } = req.body;

    if (fromDayIndex === undefined || toDayIndex === undefined || !orderId) {
      return res.status(400).json({ message: 'Order ID, from day index, and to day index are required' });
    }

    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    if (fromDayIndex < 0 || fromDayIndex >= planning.days.length ||
        toDayIndex < 0 || toDayIndex >= planning.days.length) {
      return res.status(400).json({ message: 'Invalid day index' });
    }

    // Verify the order exists in the source day
    const orderExistsInSourceDay = planning.days[fromDayIndex].orders.some(
      id => id.toString() === orderId
    );

    if (!orderExistsInSourceDay) {
      return res.status(400).json({ message: 'Order not found in the specified source day' });
    }

    // Remove order from source day
    planning.days[fromDayIndex].orders = planning.days[fromDayIndex].orders.filter(
      id => id.toString() !== orderId
    );

    // Add order to destination day (avoid duplicates)
    const targetDayOrderIds = planning.days[toDayIndex].orders.map(id => id.toString());
    if (!targetDayOrderIds.includes(orderId)) {
      planning.days[toDayIndex].orders.push(orderId);
    }

    await planning.save();

    // Update order's launchDate to the new day's date
    const newLaunchDate = planning.days[toDayIndex].date;
    await Order.findByIdAndUpdate(orderId, { launchDate: newLaunchDate });

    // Return updated planning with populated data
    const updatedPlanning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    res.status(200).json({
      message: `Order moved from ${planning.days[fromDayIndex].dayName || `day ${fromDayIndex}`} to ${planning.days[toDayIndex].dayName || `day ${toDayIndex}`}`,
      planning: updatedPlanning
    });
  } catch (err) {
    console.error('Error moving order:', err);
    res.status(500).json({ message: 'Failed to move order', error: err.message });
  }
});

// PUT update work times for specific days in planning
router.put('/:id/work-times', async (req, res) => {
  try {
    const { workTimes } = req.body; // Array of { dayIndex, start, end, isWorkingDay }

    if (!Array.isArray(workTimes)) {
      return res.status(400).json({ message: 'workTimes must be an array' });
    }

    const planning = await PlanningSemaine.findById(req.params.id);
    if (!planning) {
      return res.status(404).json({ message: 'Planning not found' });
    }

    // Update work times for specified days
    workTimes.forEach(({ dayIndex, start, end, isWorkingDay }) => {
      if (dayIndex >= 0 && dayIndex < planning.days.length) {
        if (start !== undefined) planning.days[dayIndex].workTime.start = start;
        if (end !== undefined) planning.days[dayIndex].workTime.end = end;
        if (isWorkingDay !== undefined) planning.days[dayIndex].workTime.isWorkingDay = isWorkingDay;
      }
    });

    await planning.save();

    const updatedPlanning = await PlanningSemaine.findById(req.params.id)
      .populate({
        path: 'days.orders',
        populate: { path: 'article' }
      });

    res.status(200).json({
      message: 'Work times updated successfully',
      planning: updatedPlanning
    });
  } catch (err) {
    console.error('Error updating work times:', err);
    res.status(500).json({ message: 'Failed to update work times', error: err.message });
  }
});



module.exports = router;
