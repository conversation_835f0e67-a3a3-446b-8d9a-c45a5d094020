// routes/stats.js
const express = require('express');
const Order = require('../models/Order');
const Packet = require('../models/Packet');
const Piece = require('../models/Piece');
const Colis = require('../models/Colis');
const User = require('../models/User');

const router = express.Router();

// GET /stats/production - Overall production statistics
router.get('/production', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Build date filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
    }

    // Get all orders with populated data
    const orders = await Order.find(dateFilter)
      .populate({
        path: 'colis',
        populate: {
          path: 'packets',
          populate: { path: 'pieces' }
        }
      });

    // Calculate statistics
    const stats = {
      orders: {
        total: orders.length,
        pending: orders.filter(o => o.status === 'pending').length,
        in_progress: orders.filter(o => o.status === 'in_progress').length,
        finnishing: orders.filter(o => o.status === 'finnishing').length,
        completed: orders.filter(o => o.status === 'completed').length,
        blocked: orders.filter(o => o.bloquer).length
      },
      colis: {
        total: 0,
        pending: 0,
        in_progress: 0,
        retouche: 0,
        finnishing: 0,
        completed: 0
      },
      packets: {
        total: 0,
        pending: 0,
        in_progress: 0,
        retouche: 0,
        finnishing: 0,
        completed: 0,
        blocked: 0
      },
      pieces: {
        total: 0,
        pending: 0,
        in_progress: 0,
        faulted: 0,
        finnishing: 0,
        completed: 0
      },
      productivity: {
        completionRate: 0,
        averageTimePerOrder: 0,
        totalScans: 0
      }
    };

    let totalScans = 0;
    let completedOrdersTime = 0;
    let completedOrdersCount = 0;

    // Process each order
    orders.forEach(order => {
      totalScans += order.scans.length;

      // Calculate order completion time for completed orders
      if (order.status === 'completed' && order.scans.length > 0) {
        const firstScan = order.scans.find(s => s.type === 'EM');
        const lastScan = order.scans.find(s => s.type === 'SF');
        if (firstScan && lastScan) {
          const timeDiff = new Date(lastScan.time) - new Date(firstScan.time);
          completedOrdersTime += timeDiff;
          completedOrdersCount++;
        }
      }

      // Process colis
      order.colis.forEach(colis => {
        stats.colis.total++;
        stats.colis[colis.status] = (stats.colis[colis.status] || 0) + 1;

        // Process packets
        colis.packets.forEach(packet => {
          stats.packets.total++;
          stats.packets[packet.status] = (stats.packets[packet.status] || 0) + 1;
          if (packet.bloquer) stats.packets.blocked++;

          totalScans += packet.scans.length;

          // Process pieces
          packet.pieces.forEach(piece => {
            stats.pieces.total++;
            stats.pieces[piece.status] = (stats.pieces[piece.status] || 0) + 1;
          });
        });
      });
    });

    // Calculate productivity metrics
    stats.productivity.completionRate = stats.orders.total > 0
      ? ((stats.orders.completed / stats.orders.total) * 100).toFixed(2)
      : 0;

    stats.productivity.averageTimePerOrder = completedOrdersCount > 0
      ? Math.round(completedOrdersTime / completedOrdersCount / (1000 * 60 * 60)) // Convert to hours
      : 0;

    stats.productivity.totalScans = totalScans;

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error fetching production stats:', err);
    res.status(500).json({ message: 'Failed to fetch production statistics', error: err.message });
  }
});

// GET /stats/user-performance - User performance statistics
router.get('/user-performance', async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;

    // Build date filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
    }

    // Get all orders and packets with scans
    const orders = await Order.find(dateFilter);
    const packets = await Packet.find();

    const userStats = {};

    // Process order scans
    orders.forEach(order => {
      order.scans.forEach(scan => {
        if (scan.user && (!userId || scan.user === userId)) {
          if (!userStats[scan.user]) {
            userStats[scan.user] = {
              userId: scan.user,
              orderScans: { EM: 0, SM: 0, SF: 0 },
              packetScans: { debutGM: 0, finGM: 0, ctrlFinCh: 0, finFinition: 0, operations: 0 },
              totalScans: 0,
              lastScanDate: null
            };
          }

          userStats[scan.user].orderScans[scan.type] = (userStats[scan.user].orderScans[scan.type] || 0) + 1;
          userStats[scan.user].totalScans++;

          if (!userStats[scan.user].lastScanDate || new Date(scan.time) > new Date(userStats[scan.user].lastScanDate)) {
            userStats[scan.user].lastScanDate = scan.time;
          }
        }
      });
    });

    // Process packet scans
    packets.forEach(packet => {
      packet.scans.forEach(scan => {
        if (scan.user && (!userId || scan.user === userId)) {
          if (!userStats[scan.user]) {
            userStats[scan.user] = {
              userId: scan.user,
              orderScans: { EM: 0, SM: 0, SF: 0 },
              packetScans: { debutGM: 0, finGM: 0, ctrlFinCh: 0, finFinition: 0, operations: 0 },
              totalScans: 0,
              lastScanDate: null
            };
          }

          if (['debutGM', 'finGM', 'ctrlFinCh', 'finFinition'].includes(scan.type)) {
            userStats[scan.user].packetScans[scan.type]++;
          } else {
            userStats[scan.user].packetScans.operations++;
          }

          userStats[scan.user].totalScans++;

          if (!userStats[scan.user].lastScanDate || new Date(scan.time) > new Date(userStats[scan.user].lastScanDate)) {
            userStats[scan.user].lastScanDate = scan.time;
          }
        }
      });
    });

    res.status(200).json(Object.values(userStats));
  } catch (err) {
    console.error('Error fetching user performance stats:', err);
    res.status(500).json({ message: 'Failed to fetch user performance statistics', error: err.message });
  }
});

// GET /stats/daily-production - Daily production statistics
router.get('/daily-production', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const orders = await Order.find({
      createdAt: { $gte: startDate }
    }).populate({
      path: 'colis',
      populate: { path: 'packets' }
    });

    const dailyStats = {};

    // Initialize daily stats for the last N days
    for (let i = 0; i < parseInt(days); i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateKey = date.toISOString().split('T')[0];

      dailyStats[dateKey] = {
        date: dateKey,
        ordersCreated: 0,
        ordersCompleted: 0,
        packetsCompleted: 0,
        totalScans: 0
      };
    }

    // Process orders
    orders.forEach(order => {
      const createdDate = new Date(order.createdAt).toISOString().split('T')[0];
      if (dailyStats[createdDate]) {
        dailyStats[createdDate].ordersCreated++;
      }

      // Check completion date from SF scan
      const sfScan = order.scans.find(s => s.type === 'SF');
      if (sfScan) {
        const completedDate = new Date(sfScan.time).toISOString().split('T')[0];
        if (dailyStats[completedDate]) {
          dailyStats[completedDate].ordersCompleted++;
        }
      }

      // Count daily scans
      order.scans.forEach(scan => {
        const scanDate = new Date(scan.time).toISOString().split('T')[0];
        if (dailyStats[scanDate]) {
          dailyStats[scanDate].totalScans++;
        }
      });

      // Count packet completions
      order.colis.forEach(colis => {
        colis.packets.forEach(packet => {
          const finFinitionScan = packet.scans.find(s => s.type === 'finFinition');
          if (finFinitionScan) {
            const completedDate = new Date(finFinitionScan.time).toISOString().split('T')[0];
            if (dailyStats[completedDate]) {
              dailyStats[completedDate].packetsCompleted++;
            }
          }

          // Count packet scans
          packet.scans.forEach(scan => {
            const scanDate = new Date(scan.time).toISOString().split('T')[0];
            if (dailyStats[scanDate]) {
              dailyStats[scanDate].totalScans++;
            }
          });
        });
      });
    });

    const result = Object.values(dailyStats).sort((a, b) => new Date(a.date) - new Date(b.date));
    res.status(200).json(result);
  } catch (err) {
    console.error('Error fetching daily production stats:', err);
    res.status(500).json({ message: 'Failed to fetch daily production statistics', error: err.message });
  }
});

// GET /stats/defects - Defect analysis statistics
router.get('/defects', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
    }

    const orders = await Order.find(dateFilter).populate({
      path: 'colis',
      populate: {
        path: 'packets',
        populate: { path: 'pieces' }
      }
    });

    const defectStats = {
      totalPacketsWithDefects: 0,
      totalPiecesWithDefects: 0,
      packetDefects: {},
      pieceDefects: {},
      colisProblems: {},
      retouchePackets: 0,
      blockedOrders: 0,
      blockedPackets: 0
    };

    orders.forEach(order => {
      if (order.bloquer) defectStats.blockedOrders++;

      order.colis.forEach(colis => {
        // Count colis problems
        if (colis.problems && colis.problems.length > 0) {
          colis.problems.forEach(problem => {
            defectStats.colisProblems[problem] = (defectStats.colisProblems[problem] || 0) + 1;
          });
        }

        colis.packets.forEach(packet => {
          if (packet.bloquer) defectStats.blockedPackets++;
          if (packet.status === 'retouche') defectStats.retouchePackets++;

          // Count packet defects
          if (packet.defaut && packet.defaut.length > 0) {
            defectStats.totalPacketsWithDefects++;
            packet.defaut.forEach(defect => {
              defectStats.packetDefects[defect] = (defectStats.packetDefects[defect] || 0) + 1;
            });
          }

          // Count piece defects
          packet.pieces.forEach(piece => {
            if (piece.defaut && piece.defaut.length > 0) {
              defectStats.totalPiecesWithDefects++;
              piece.defaut.forEach(defect => {
                defectStats.pieceDefects[defect] = (defectStats.pieceDefects[defect] || 0) + 1;
              });
            }
          });
        });
      });
    });

    res.status(200).json(defectStats);
  } catch (err) {
    console.error('Error fetching defect stats:', err);
    res.status(500).json({ message: 'Failed to fetch defect statistics', error: err.message });
  }
});

// GET /stats/operations - Operations performance statistics
router.get('/operations', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const packets = await Packet.find();

    const operationStats = {};
    let totalOperationScans = 0;

    packets.forEach(packet => {
      packet.scans.forEach(scan => {
        // Filter by date if provided
        if (startDate && new Date(scan.time) < new Date(startDate)) return;
        if (endDate && new Date(scan.time) > new Date(endDate)) return;

        // Count operation scans (exclude system scans)
        if (!['debutGM', 'finGM', 'ctrlFinCh', 'finFinition', 'packetBloque', 'packetDebloque'].includes(scan.type)) {
          const operationName = scan.type.replace(' Terminé', '');

          if (!operationStats[operationName]) {
            operationStats[operationName] = {
              name: operationName,
              totalScans: 0,
              uniquePackets: new Set(),
              averageTimePerScan: 0,
              users: new Set()
            };
          }

          operationStats[operationName].totalScans++;
          operationStats[operationName].uniquePackets.add(packet._id.toString());
          if (scan.user) operationStats[operationName].users.add(scan.user);
          totalOperationScans++;
        }
      });
    });

    // Convert sets to counts and calculate percentages
    const result = Object.values(operationStats).map(op => ({
      name: op.name,
      totalScans: op.totalScans,
      uniquePackets: op.uniquePackets.size,
      uniqueUsers: op.users.size,
      percentage: totalOperationScans > 0 ? ((op.totalScans / totalOperationScans) * 100).toFixed(2) : 0
    })).sort((a, b) => b.totalScans - a.totalScans);

    res.status(200).json({
      totalOperationScans,
      operations: result
    });
  } catch (err) {
    console.error('Error fetching operation stats:', err);
    res.status(500).json({ message: 'Failed to fetch operation statistics', error: err.message });
  }
});

// GET /stats/timeline - Production timeline statistics
router.get('/timeline', async (req, res) => {
  try {
    const { orderId } = req.query;

    if (!orderId) {
      return res.status(400).json({ message: 'Order ID is required' });
    }

    const order = await Order.findById(orderId).populate({
      path: 'colis',
      populate: {
        path: 'packets',
        populate: { path: 'pieces' }
      }
    });

    if (!order) {
      return res.status(404).json({ message: 'Order not found' });
    }

    const timeline = {
      order: {
        id: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        createdAt: order.createdAt,
        scans: order.scans.sort((a, b) => new Date(a.time) - new Date(b.time))
      },
      colis: [],
      totalDuration: null,
      phases: {
        montage: { start: null, end: null, duration: null },
        finition: { start: null, end: null, duration: null }
      }
    };

    // Calculate order phases
    const emScan = order.scans.find(s => s.type === 'EM');
    const smScan = order.scans.find(s => s.type === 'SM');
    const sfScan = order.scans.find(s => s.type === 'SF');

    if (emScan && smScan) {
      timeline.phases.montage.start = emScan.time;
      timeline.phases.montage.end = smScan.time;
      timeline.phases.montage.duration = new Date(smScan.time) - new Date(emScan.time);
    }

    if (smScan && sfScan) {
      timeline.phases.finition.start = smScan.time;
      timeline.phases.finition.end = sfScan.time;
      timeline.phases.finition.duration = new Date(sfScan.time) - new Date(smScan.time);
    }

    if (emScan && sfScan) {
      timeline.totalDuration = new Date(sfScan.time) - new Date(emScan.time);
    }

    // Process each colis
    order.colis.forEach(colis => {
      const colisData = {
        id: colis._id,
        numeroColis: colis.numeroColis,
        status: colis.status,
        packets: []
      };

      colis.packets.forEach(packet => {
        const packetData = {
          id: packet._id,
          numero: packet.numero,
          status: packet.status,
          bloquer: packet.bloquer,
          scans: packet.scans.sort((a, b) => new Date(a.time) - new Date(b.time)),
          duration: null
        };

        // Calculate packet duration
        const firstScan = packet.scans.find(s => s.type === 'debutGM');
        const lastScan = packet.scans.find(s => s.type === 'finFinition');

        if (firstScan && lastScan) {
          packetData.duration = new Date(lastScan.time) - new Date(firstScan.time);
        }

        colisData.packets.push(packetData);
      });

      timeline.colis.push(colisData);
    });

    res.status(200).json(timeline);
  } catch (err) {
    console.error('Error fetching timeline stats:', err);
    res.status(500).json({ message: 'Failed to fetch timeline statistics', error: err.message });
  }
});

module.exports = router;
