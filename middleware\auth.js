// middleware/auth.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');
require('dotenv').config();

const auth = async (req, res, next) => {
    const authHeader = req.header('Authorization');
  

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }
  
    const token = authHeader.split(' ')[1]; // Extract token after "Bearer"
  
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
      req.user = decoded; // Add decoded user info to request
      next();
    } catch (err) {
      res.status(401).json({ message: 'Token is not valid' });
    }
  };

// Middleware to ensure the user is an Admin
const admin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Access denied, admin only' });
  }
  next();
};

module.exports = { auth, admin };
