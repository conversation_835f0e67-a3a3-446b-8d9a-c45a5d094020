const express = require('express');
const User = require('../models/User'); // Import the User model
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { auth, admin } = require('../middleware/auth');
const router = express.Router();



// GET: Retrieve all users (only <PERSON><PERSON> can access)
router.get('/', auth, admin, async (req, res) => {
  try {
    const users = await User.find().select('-password'); // exclude password
    res.status(200).json(users);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch users', error: err.message });
  }
});


// GET: Retrieve a user by identifiant
router.get('/by-identifiant/:identifiant', auth, async (req, res) => {
  try {
    const { identifiant } = req.params;

    const user = await User.findOne({ identifiant }).select('-password'); // Exclude password
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user);
  } catch (err) {
    console.log('err fetch user by identifiant is', err.message);
    res.status(500).json({ message: 'Failed to fetch user', error: err.message });
  }
});

// GET: Retrieve a user by ID (Admin only)
router.get('/:id', auth, async (req, res) => {
  try {
    const userId = req.params.id;

    const user = await User.findById(userId).select('-password'); // Exclude password
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json(user);
  } catch (err) {
    console.log('err fetch user is',err.message)
    res.status(500).json({ message: 'Failed to fetch user', error: err.message });

  }
});



// POST: Create a new user (only Admin can create users)
router.post('/',auth,admin, async (req, res) => {
  try {
    const { name, email, cin, identifiant, password, role } = req.body;

    // Check if the email, CIN, or identifiant is already registered
    const existingUser = await User.findOne({
      $or: [{ email }, { cin }, { identifiant }]
    });
    if (existingUser) {
      return res.status(400).json({ message: 'Email, CIN, or identifiant already exists' });
    }

    // Create the new user
    const newUser = new User({ name, email, cin, identifiant, password, role: role || 'user' });
    await newUser.save();

    // Generate JWT token for the new user
    const token = newUser.generateAuthToken();
    res.status(201).json({ message: 'User created successfully', token });
  } catch (err) {
    res.status(500).json({ message: 'Failed to create user', error: err.message });
  }
});

// POST: Login (Authenticate with JWT)
router.post('/login', async (req, res) => {
  try {
    const { email, identifiant, password } = req.body;

    // Check if user exists by email or identifiant
    let user;
    if (email) {
      user = await User.findOne({ email });
    } else if (identifiant) {
      user = await User.findOne({ identifiant });
    } else {
      return res.status(400).json({ message: 'Email or identifiant is required' });
    }

    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Compare the entered password with the stored password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token and send it in the response
    const token = user.generateAuthToken();
    res.status(200).json({ message: 'Login successful', token });
  } catch (err) {
    res.status(500).json({ message: 'Login failed', error: err.message });
  }
});

// PUT: Update user details (including password)
router.put('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const { name, email, cin, identifiant, password, role } = req.body;

    // Find the user by ID
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if email, cin, or identifiant already exists for another user
    if (email || cin || identifiant) {
      const existingUser = await User.findOne({
        _id: { $ne: userId }, // Exclude current user
        $or: [
          ...(email ? [{ email }] : []), 
          ...(cin ? [{ cin }] : []),  
          ...(identifiant ? [{ identifiant }] : [])
        ]
      });

      if (existingUser) {
        return res.status(400).json({ message: 'Email, CIN, or identifiant already exists for another user' });
      }
    }

    // Update user details
    if (name) user.name = name;
    if (email) user.email = email;
    if (cin) user.cin = cin;
    if (identifiant) user.identifiant = identifiant;
    if (role) user.role = role;

    // If a new password is provided, hash it and update it
    if (password) {
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(password, salt);
    }

    // Save the updated user
    await user.save();
    res.status(200).json({ message: 'User updated successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to update user', error: err.message });
  }
});


// DELETE: Delete a user by ID (only Admin can delete users)
router.delete('/:id', auth, admin, async (req, res) => {
  try {
    const userId = req.params.id;

    // Find the user by ID and delete
    const user = await User.findByIdAndDelete(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({ message: 'User deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to delete user', error: err.message });
  }
});

module.exports = router;
