// routes/machines.js
const express = require('express');
const Machine = require('../models/Machine');
const Operation = require('../models/Operation');
const User = require('../models/User');
const { auth, admin } = require('../middleware/auth');

const router = express.Router();

// GET all machines
router.get('/', auth, async (req, res) => {
  try {
    const machines = await Machine.find()
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name')
      .sort({ createdAt: -1 });
    
    res.status(200).json(machines);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch machines', error: err.message });
  }
});

// GET machine by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const machine = await Machine.findById(req.params.id)
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name');
    
    if (!machine) {
      return res.status(404).json({ message: 'Machine not found' });
    }
    
    res.status(200).json(machine);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch machine', error: err.message });
  }
});

// POST create new machine
router.post('/', auth, admin, async (req, res) => {
  try {
    const { 
      model, 
      reference, 
      dateMiseEnMarche, 
      dateAchat, 
      status, 
      assignedWorker, 
      operations 
    } = req.body;

    // Check if reference already exists
    const existingMachine = await Machine.findOne({ reference });
    if (existingMachine) {
      return res.status(400).json({ message: 'Machine with this reference already exists' });
    }

    // Validate assigned worker if provided
    if (assignedWorker) {
      const worker = await User.findById(assignedWorker);
      if (!worker) {
        return res.status(400).json({ message: 'Assigned worker not found' });
      }
    }

    // Handle operations - create if they don't exist
    const operationIds = [];
    if (operations && operations.length > 0) {
      for (const operationName of operations) {
        // Check if operation exists
        let operation = await Operation.findOne({ name: operationName });
        
        // If operation doesn't exist, create it
        if (!operation) {
          operation = new Operation({ name: operationName });
          await operation.save();
          console.log(`Created new operation: ${operationName}`);
        }
        
        operationIds.push(operation._id);
      }
    }

    // Create the machine
    const newMachine = new Machine({
      model,
      reference,
      dateMiseEnMarche: new Date(dateMiseEnMarche),
      dateAchat: new Date(dateAchat),
      status: status || 'available',
      assignedWorker: assignedWorker || null,
      operations: operationIds
    });

    await newMachine.save();

    // Populate the response
    const populatedMachine = await Machine.findById(newMachine._id)
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name');

    res.status(201).json({ 
      message: 'Machine created successfully', 
      machine: populatedMachine 
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to create machine', error: err.message });
  }
});

// PUT update machine
router.put('/:id', auth, admin, async (req, res) => {
  try {
    const machineId = req.params.id;
    const { 
      model, 
      reference, 
      dateMiseEnMarche, 
      dateAchat, 
      status, 
      assignedWorker, 
      operations 
    } = req.body;

    // Find the machine
    const machine = await Machine.findById(machineId);
    if (!machine) {
      return res.status(404).json({ message: 'Machine not found' });
    }

    // Check if reference already exists for another machine
    if (reference && reference !== machine.reference) {
      const existingMachine = await Machine.findOne({ 
        reference, 
        _id: { $ne: machineId } 
      });
      if (existingMachine) {
        return res.status(400).json({ message: 'Machine with this reference already exists' });
      }
    }

    // Validate assigned worker if provided
    if (assignedWorker) {
      const worker = await User.findById(assignedWorker);
      if (!worker) {
        return res.status(400).json({ message: 'Assigned worker not found' });
      }
    }

    // Handle operations - create if they don't exist
    let operationIds = machine.operations;
    if (operations && operations.length > 0) {
      operationIds = [];
      for (const operationName of operations) {
        // Check if operation exists
        let operation = await Operation.findOne({ name: operationName });
        
        // If operation doesn't exist, create it
        if (!operation) {
          operation = new Operation({ name: operationName });
          await operation.save();
          console.log(`Created new operation: ${operationName}`);
        }
        
        operationIds.push(operation._id);
      }
    }

    // Update machine fields
    if (model) machine.model = model;
    if (reference) machine.reference = reference;
    if (dateMiseEnMarche) machine.dateMiseEnMarche = new Date(dateMiseEnMarche);
    if (dateAchat) machine.dateAchat = new Date(dateAchat);
    if (status) machine.status = status;
    if (assignedWorker !== undefined) machine.assignedWorker = assignedWorker;
    if (operations) machine.operations = operationIds;

    await machine.save();

    // Populate the response
    const populatedMachine = await Machine.findById(machine._id)
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name');

    res.status(200).json({ 
      message: 'Machine updated successfully', 
      machine: populatedMachine 
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to update machine', error: err.message });
  }
});

// DELETE machine
router.delete('/:id', auth, admin, async (req, res) => {
  try {
    const machine = await Machine.findByIdAndDelete(req.params.id);
    
    if (!machine) {
      return res.status(404).json({ message: 'Machine not found' });
    }
    
    res.status(200).json({ message: 'Machine deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to delete machine', error: err.message });
  }
});

// GET machines by status
router.get('/status/:status', auth, async (req, res) => {
  try {
    const { status } = req.params;
    
    // Validate status
    const validStatuses = ['available', 'in_maintenance', 'assigned', 'broken'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }
    
    const machines = await Machine.find({ status })
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name')
      .sort({ createdAt: -1 });
    
    res.status(200).json(machines);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch machines by status', error: err.message });
  }
});

// GET machines by assigned worker
router.get('/worker/:workerId', auth, async (req, res) => {
  try {
    const { workerId } = req.params;
    
    const machines = await Machine.find({ assignedWorker: workerId })
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name')
      .sort({ createdAt: -1 });
    
    res.status(200).json(machines);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch machines by worker', error: err.message });
  }
});

// POST assign worker to machine
router.post('/:id/assign-worker', auth, admin, async (req, res) => {
  try {
    const { workerId } = req.body;
    const machineId = req.params.id;
    
    // Find the machine
    const machine = await Machine.findById(machineId);
    if (!machine) {
      return res.status(404).json({ message: 'Machine not found' });
    }
    
    // Validate worker
    if (workerId) {
      const worker = await User.findById(workerId);
      if (!worker) {
        return res.status(400).json({ message: 'Worker not found' });
      }
    }
    
    // Update machine
    machine.assignedWorker = workerId || null;
    machine.status = workerId ? 'assigned' : 'available';
    await machine.save();
    
    // Populate the response
    const populatedMachine = await Machine.findById(machine._id)
      .populate('assignedWorker', 'name email identifiant role')
      .populate('operations', 'name');
    
    res.status(200).json({ 
      message: workerId ? 'Worker assigned successfully' : 'Worker unassigned successfully', 
      machine: populatedMachine 
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to assign worker', error: err.message });
  }
});

module.exports = router;
