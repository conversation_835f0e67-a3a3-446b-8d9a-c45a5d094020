const express = require('express');
const Packet = require('../models/Packet');
const Piece = require('../models/Piece');
const Order = require('../models/Order');
const Colis = require('../models/Colis');

const router = express.Router();

// PUT to update a packet by ID
router.put('/:id', async (req, res) => {
  try {
    const packetId = req.params.id;
    const { name, pieces, scan, bloquer } = req.body; // added bloquer field

    const packet = await Packet.findById(packetId).populate('pieces.piece');
    if (!packet) {
      return res.status(404).json({ message: 'Packet not found' });
    }

    if (name) packet.name = name;
    if (bloquer !== undefined) packet.bloquer = bloquer; // Update bloquer field if provided

    // Append a scan if provided
    if (scan && typeof scan.scanNumber === 'number') {
      packet.scans.push({
        scanNumber: scan.scanNumber,
        date: new Date()
      });
    }

    // Update pieces if provided
    if (pieces) {
      const updatedPieces = await Promise.all(pieces.map(async (pieceData) => {
        const piece = await Piece.findById(pieceData.pieceId);
        if (!piece) {
          throw new Error(`Piece with ID ${pieceData.pieceId} not found`);
        }
        return {
          piece: piece._id,
          quantity: pieceData.quantity,
        };
      }));

      packet.pieces = updatedPieces;
    }

    await packet.save();
    res.status(200).json(packet);
  } catch (err) {
    res.status(500).json({ message: 'Failed to update packet', error: err.message });
  }
});



// scan packet

// POST /packets/scan
router.post('/scan', async (req, res) => {
  try {
    const { qr } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user
    if (!qr) return res.status(400).json({ message: 'QR code is required' });

    // ✅ Extract orderNumber from QR (format: orderNumber/C1/P1)
    const qrParts = qr.split('/');
    if (qrParts.length < 3) {
      return res.status(400).json({ message: 'Format de Barcode non valide. Scanner un barcode de packet' });
    }

    const orderNumber = qrParts[0].trim();

    // ✅ Find the packet using full QR code
    const packet = await Packet.findOne({ qrCode: qr });
    if (!packet) return res.status(404).json({ message: 'Packet not found for given QR code' });

    // ✅ Check if the user has already scanned this packet (except for admin users)
    const userRole = req.user.role; // Get user role from JWT token
    const userAlreadyScanned = packet.scans.some(scan => (scan.user === userId && (scan.type !== 'debutGM')));
    if (userAlreadyScanned && userRole !== 'admin') {
      return res.status(400).json({ message: 'Vous avez déjà scanné cette packet' });
    }

    // ✅ Find the order using orderNumber and populate deeply
    const order = await Order.findOne({ orderNumber }).populate({
      path: 'article',
      populate: {
        path: 'gamme',
        populate: {
          path: 'operations.operation',
        },
      },
    });

    if (!order) return res.status(404).json({ message: 'Order not found for given orderNumber' });

    // Check if the order is blocked
    if (order.bloquer) {
      return res.status(400).json({ message: 'L\'ordre est bloqué' });
    }

    // 🚫 Do nothing if the order is not in progress
    if (order.status !== 'in_progress') {
      return res.status(200).json({ message: `OF n'est pas en cours. Aucun scan n'a été enregistré.` });
    }

    const gamme = order.article?.gamme;
    if (!gamme) return res.status(400).json({ message: 'Aucune gamme associée à cet article' });

    const scanNow = {
      time: new Date(),
      user: userId // Add userId to the scan
    };

    // ✅ Handle first scan
    if (packet.scans.length === 0) {
      scanNow.type = 'debutGM';
      packet.scans.push(scanNow);
      packet.status = 'in_progress'; // ✅ Update packet status

      // Update all pieces status to in_progress
      for (const pieceId of packet.pieces) {
        await Piece.findByIdAndUpdate(pieceId, {
          status: 'in_progress'
        });
      }

      await packet.save();

      // Update colis status based on packet status change
      await updateColisStatusBasedOnPackets(packet.colis);

      await checkIfOrderShouldBeCompleted(order._id);
      return res.status(200).json({ message: 'Le paquet est lancé', packet });
    }

    // ✅ Ensure "debutGM" exists
    const hasDebut = packet.scans.some(s => s.type === 'debutGM');
    if (!hasDebut) {
      scanNow.type = 'debutGM';
      packet.scans.push(scanNow);
      packet.status = 'in_progress'; // ✅ Update packet status

      // Update all pieces status to in_progress
      for (const pieceId of packet.pieces) {
        await Piece.findByIdAndUpdate(pieceId, {
          status: 'in_progress'
        });
      }

      await packet.save();

      // Update colis status based on packet status change
      await updateColisStatusBasedOnPackets(packet.colis);

      await checkIfOrderShouldBeCompleted(order._id);
      return res.status(200).json({ message: 'Le paquet a etait lancé', packet });
    }

    // ✅ Get scanPoint operations sorted by ordre
    const scanOps = gamme.operations
      .filter(op => op.scanPoint)
      .sort((a, b) => a.ordre - b.ordre);

    // ✅ Extract operation names already scanned
    const doneOps = packet.scans
      .filter(s => s.type !== 'debutGM' && s.type !== 'finGM')
      .map(s => s.type.replace(' Terminé', ''));

    // ✅ Find the next operation to scan
    const nextOp = scanOps.find(op => !doneOps.includes(op.operation.name));

    if (!nextOp) {
      // ✅ All operations scanned already — no action needed
      return res.status(200).json({ message: 'Toutes les scans sont terminées', packet });
    }

    // ✅ Check if it's the last operation in gamme
    const isLastScanOp = scanOps.at(-1).operation.name === nextOp.operation.name;

    if (isLastScanOp) {
      // ✅ Final operation scan and add finGM together
      const now = new Date();
      packet.scans.push({
        type: `${nextOp.operation.name} Terminé`,
        time: now,
        user: userId
      });
      packet.scans.push({
        type: 'finGM',
        time: now,
        user: userId
      });

      await packet.save();
      await checkIfOrderShouldBeCompleted(order._id);
      return res.status(200).json({ message: `'${nextOp.operation.name} est terminé'`, packet });
    }

    // ✅ Add intermediate operation scan
    scanNow.type = `${nextOp.operation.name} Terminé`;
    packet.scans.push(scanNow);
    await packet.save();
    await checkIfOrderShouldBeCompleted(order._id);
    res.status(200).json({ message: ` '${nextOp.operation.name}' Terminé`, packet });

  } catch (err) {
    console.error('Scan error:', err);
    res.status(500).json({ message: 'Failed to process scan', error: err.message });
  }
});


async function checkIfOrderShouldBeCompleted(orderId) {
  const orderWithColis = await Order.findById(orderId).populate({
    path: 'colis',
    populate: {
      path: 'packets'
    }
  });

  // Check if all non-blocked packets across all colis have finGM scan
  let allPacketsCompleted = true;
  let finGMUser = '';

  // Iterate through all colis and their packets
  for (const colis of orderWithColis.colis) {
    for (const packet of colis.packets) {
      // Skip blocked packets - they are ignored in completion logic
      if (packet.bloquer) continue;

      const hasFinGM = packet.scans.some(scan => scan.type === 'finGM');
      if (!hasFinGM) {
        allPacketsCompleted = false;
      } else if (!finGMUser) {
        // Get the user from the first finGM scan we find
        finGMUser = packet.scans.find(scan => scan.type === 'finGM')?.user || '';
      }
    }
  }

  if (allPacketsCompleted && orderWithColis.status !== 'completed') {
    orderWithColis.scans.push({
      type: 'SM',
      time: new Date(),
      user: finGMUser
    }); // ✅ Add "Sortie Montage" scan
    await orderWithColis.save();
  }
}

// Update colis status based on its packets' statuses
async function updateColisStatusBasedOnPackets(colisId) {
  const colis = await Colis.findById(colisId).populate('packets');
  if (!colis) return;

  const packets = colis.packets;
  if (packets.length === 0) return;

  // Filter out blocked packets - they are ignored in status logic
  const nonBlockedPackets = packets.filter(packet => !packet.bloquer);
  if (nonBlockedPackets.length === 0) return;

  // Determine the appropriate colis status based on non-blocked packet statuses
  let newColisStatus = colis.status;

  // Check packet statuses (only non-blocked packets)
  const hasInProgress = nonBlockedPackets.some(packet => packet.status === 'in_progress');
  const hasRetouche = nonBlockedPackets.some(packet => packet.status === 'retouche');
  const allFinnishing = nonBlockedPackets.every(packet => packet.status === 'finnishing');
  const allCompleted = nonBlockedPackets.every(packet => packet.status === 'completed');

  // Determine new status based on priority:
  // 1. If any packet is in retouche -> colis should be retouche
  // 2. If all packets are completed -> colis should be completed
  // 3. If all packets are finnishing -> colis should be finnishing
  // 4. If any packet is in_progress -> colis should be in_progress
  // 5. Otherwise keep current status

  if (hasRetouche) {
    newColisStatus = 'retouche';
  } else if (allCompleted) {
    newColisStatus = 'completed';
  } else if (allFinnishing) {
    newColisStatus = 'finnishing';
  } else if (hasInProgress) {
    newColisStatus = 'in_progress';
  }

  // Update colis status if it has changed
  if (newColisStatus !== colis.status) {
    colis.status = newColisStatus;
    await colis.save();
  }
}

// Check if all packets in an order are in "finnishing" status and update order status accordingly
async function checkIfOrderShouldBeFinishing(orderId) {
  const orderWithColis = await Order.findById(orderId).populate({
    path: 'colis',
    populate: {
      path: 'packets'
    }
  });

  // Check if all non-blocked packets across all colis have "finnishing" status
  let allPacketsFinishing = true;
  let ctrlFinChUser = '';

  // Iterate through all colis and their packets
  for (const colis of orderWithColis.colis) {
    // Update colis status based on its non-blocked packets
    let allColisPacketsFinishing = true;

    for (const packet of colis.packets) {
      // Skip blocked packets - they are ignored in completion logic
      if (packet.bloquer) continue;

      if (packet.status !== 'finnishing') {
        allPacketsFinishing = false;
        allColisPacketsFinishing = false;
      } else if (!ctrlFinChUser) {
        // Get the user from the first ctrlFinCh scan we find
        ctrlFinChUser = packet.scans.find(scan => scan.type === 'ctrlFinCh')?.user || '';
      }
    }

    // Update colis status if all its packets are finnishing
    if (allColisPacketsFinishing && colis.status !== 'finnishing') {
      colis.status = 'finnishing';
      await colis.save();
    }
  }

  // If all packets across all colis are in "finnishing" status, update order status to "finnishing"
  if (allPacketsFinishing && orderWithColis.status !== 'finnishing') {
    orderWithColis.status = 'finnishing';

    // Add "Sortie Montage" scan when all packets go to finnishing
    // This indicates all packets have been scanned with ctrlFinCh with no defects
    orderWithColis.scans.push({
      type: 'SM',
      time: new Date(),
      user: ctrlFinChUser
    });

    await orderWithColis.save();
    return true;
  }

  return false;
}





// GET one packet by ID
router.get('/:id', async (req, res) => {
    try {
      const packet = await Packet.findById(req.params.id).populate('pieces');

      if (!packet) {
        return res.status(404).json({ message: 'Packet not found' });
      }

      res.status(200).json(packet);
    } catch (err) {
      res.status(500).json({ message: 'Failed to fetch packet', error: err.message });
    }
  });


// POST /packets/get-by-scan
router.post('/get-by-scan', async (req, res) => {
  try {
    const { barcode } = req.body;

    if (!barcode) {
      return res.status(400).json({ message: 'Barcode is required' });
    }

    // Find the packet using the barcode and populate pieces
    const packet = await Packet.findOne({ qrCode: barcode }).populate('pieces');
    if (!packet) {
      return res.status(404).json({ message: 'Packet not found for given barcode' });
    }

    // Extract orderNumber from barcode (format: orderNumber/C1/P1)
    const barcodeParts = barcode.split('/');
    if (barcodeParts.length < 3) {
      return res.status(400).json({ message: 'Invalid barcode format. Expected format: orderNumber/C1/P1' });
    }

    const orderNumber = barcodeParts[0].trim();

    // Find the colis that contains this packet
    const colis = await Colis.findById(packet.colis);
    if (!colis) {
      return res.status(404).json({ message: 'Colis not found for this packet' });
    }

    // Find the order using orderNumber
    const order = await Order.findOne({ orderNumber }).populate('article');
    if (!order) {
      return res.status(404).json({ message: 'Order not found for given orderNumber' });
    }

    // Return the packet with its pieces, the associated colis, and the order
    res.status(200).json({
      packet,
      colis,
      order
    });
  } catch (err) {
    console.error('Get packet by scan error:', err);
    res.status(500).json({ message: 'Failed to get packet', error: err.message });
  }
});

// POST /packets/scan-ctrl-fin-ch
router.post('/scan-ctrl-fin-ch', async (req, res) => {
  try {
    const { barcode, defautPieces } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user

    if (!barcode) {
      return res.status(400).json({ message: 'Barcode is required' });
    }

    // Find the packet using the barcode and populate pieces
    const packet = await Packet.findOne({ qrCode: barcode }).populate('pieces');
    if (!packet) {
      return res.status(404).json({ message: 'Packet not found for given barcode' });
    }

    // For ctrlFinCh, we allow the same user to scan multiple times
    // This is because packets may be returned for rework and need to be scanned again

    // Check if the packet has a finGM scan (meaning all operations are completed)
    const hasFinGM = packet.scans.some(scan => scan.type === 'finGM');
    if (!hasFinGM) {
      return res.status(400).json({
        message: 'Packet n\'est pas terminé toutes ses opérations. Veuillez terminer toutes les opérations avant le contrôle fin chaîne.',
        packet
      });
    }

    // Check if the packet is already in "finnishing" status
    if (packet.status === 'finnishing') {
      return res.status(200).json({
        message: 'Packet est déjà scanné avec non défaut et est prêt pour finition',
        packet
      });
    }

    // Check if the packet is already in "completed" status
    if (packet.status === 'completed') {
      return res.status(400).json({
        message: 'Packet est déjà terminé et ne peut plus être scanné',
        packet
      });
    }

    // Extract orderNumber from barcode (format: orderNumber/C1/P1)
    const barcodeParts = barcode.split('/');
    if (barcodeParts.length < 3) {
      return res.status(400).json({ message: 'Invalid barcode format. Expected format: orderNumber/C1/P1' });
    }

    const orderNumber = barcodeParts[0].trim();

    // Find the colis that contains this packet
    const colis = await Colis.findById(packet.colis);
    if (!colis) {
      return res.status(404).json({ message: 'Colis not found for this packet' });
    }

    // Find the order using orderNumber
    const order = await Order.findOne({ orderNumber });
    if (!order) {
      return res.status(404).json({ message: 'Order not found for given orderNumber' });
    }

    // Check if the order is blocked
    if (order.bloquer) {
      return res.status(400).json({ message: 'L\'ordre est bloqué' });
    }

    // Add a scan entry with type "ctrlFinCh"
    packet.scans.push({
      type: 'ctrlFinCh',
      time: new Date(),
      user: userId // Add userId to the scan
    });

    // Update packet and pieces status based on defautPieces
    if (defautPieces && Array.isArray(defautPieces) && defautPieces.length > 0) {
      // Case 1: Some pieces have defauts - update packet status to "retouche"
      packet.status = 'retouche';

      // Extract pieceIds from defautPieces array for easier checking
      const currentDefautPieceIds = defautPieces
        .map(entry => entry.split(':')[0])
        .filter(id => id); // Filter out any empty IDs

      // Find all pieces in this packet
      const allPieces = await Piece.find({ _id: { $in: packet.pieces } });

      // Update previously faulted pieces that are not in the current defautPieces array
      for (const piece of allPieces) {
        if (piece.status === 'faulted' && !currentDefautPieceIds.includes(piece._id.toString())) {
          // This piece was previously faulted but is not in the current defaut list
          // Update it to in_progress status
          await Piece.findByIdAndUpdate(piece._id, {
            status: 'in_progress',
            defaut: [] // Clear the defaut descriptions array
          });
        }
      }

      // Process each defaut entry (format: "pieceId:defautDescription")
      // Group defauts by pieceId
      const defautsByPieceId = {};

      for (const defautEntry of defautPieces) {
        // Split the entry into pieceId and defaut description
        const [pieceId, defautDescription] = defautEntry.split(':');

        if (pieceId && defautDescription) {
          if (!defautsByPieceId[pieceId]) {
            defautsByPieceId[pieceId] = [];
          }
          defautsByPieceId[pieceId].push(defautDescription);
        }
      }

      // Update each piece with its array of defauts
      for (const [pieceId, defautDescriptions] of Object.entries(defautsByPieceId)) {
        // Find the piece by ID and update it
        await Piece.findByIdAndUpdate(pieceId, {
          status: 'faulted',
          defaut: defautDescriptions // Store the array of defaut descriptions
        });
      }
    } else {
      // Case 2: No defauts - update packet status to "finnishing" and all pieces to "finnishing"
      packet.status = 'finnishing';

      // Update all pieces status to "finnishing"
      for (const piece of packet.pieces) {
        await Piece.findByIdAndUpdate(piece._id, {
          status: 'finnishing',
          defaut: [] // Clear any previous defauts array
        });
      }


    }

    await packet.save();

    // Update colis status based on packet status change
    await updateColisStatusBasedOnPackets(packet.colis);

    // Check if all packets in the order are now in "finnishing" status
    await checkIfOrderShouldBeFinishing(order._id);
    // Count how many times this user has scanned this packet with ctrlFinCh type
    const userScanCount = packet.scans.filter(scan =>
      scan.type === 'ctrlFinCh' && scan.user === userId
    ).length;

    res.status(200).json({
      message: defautPieces && defautPieces.length > 0
        ? `Paquet marqué pour retouche avec pièces défectueuses (scan #${userScanCount} par cet utilisateur)`
        : `Le paquet est prêt à être fini (scan #${userScanCount} par cet utilisateur)`,
      packet
    });
  } catch (err) {
    console.error('Scan ctrl fin ch error:', err);
    res.status(500).json({ message: 'Failed to process scan', error: err.message });
  }
});

// POST /packets/scan-finishing
router.post('/scan-finishing', async (req, res) => {
  try {
    const { barcode } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user

    if (!barcode) {
      return res.status(400).json({ message: 'Barcode is required' });
    }

    // Find the packet using the barcode and populate pieces
    const packet = await Packet.findOne({ qrCode: barcode }).populate('pieces');
    if (!packet) {
      return res.status(404).json({ message: 'Packet not found for given barcode' });
    }

    // Check if packet status is "finnishing"
    if (packet.status !== 'finnishing') {
      return res.status(400).json({
        message: `Le packet doit etre en etat finition pour le scanner !`,
        packet
      });
    }

    // Extract orderNumber from barcode (format: orderNumber/C1/P1)
    const barcodeParts = barcode.split('/');
    if (barcodeParts.length < 3) {
      return res.status(400).json({ message: 'Invalid barcode format. Expected format: orderNumber/C1/P1' });
    }

    const orderNumber = barcodeParts[0].trim();

    // Find the colis that contains this packet
    const colis = await Colis.findById(packet.colis);
    if (!colis) {
      return res.status(404).json({ message: 'Colis not found for this packet' });
    }

    // Find the order using orderNumber
    const order = await Order.findOne({ orderNumber }).populate({
      path: 'colis',
      populate: {
        path: 'packets',
        populate: { path: 'pieces' }
      }
    });
    if (!order) {
      return res.status(404).json({ message: 'Order not found for given orderNumber' });
    }

    // Check if the order is blocked
    if (order.bloquer) {
      return res.status(400).json({ message: 'L\'ordre est bloqué' });
    }

    // Update packet status to "completed"
    packet.status = 'completed';

    // Add finFinition scan to the packet
    packet.scans.push({
      type: 'finFinition',
      time: new Date(),
      user: userId
    });

    // Update all pieces in the packet to "completed" status
    for (const piece of packet.pieces) {
      await Piece.findByIdAndUpdate(piece._id, {
        status: 'completed'
      });
    }

    await packet.save();

    // Update colis status based on packet status change
    await updateColisStatusBasedOnPackets(packet.colis);

    // Check if all packets in the order are now completed
    await checkIfOrderShouldBeCompletedFromFinishing(order._id);

    res.status(200).json({
      message: 'Packet terminé avec succès',
      packet
    });

  } catch (err) {
    console.error('Scan finishing error:', err);
    res.status(500).json({ message: 'Failed to process finishing scan', error: err.message });
  }
});

// Helper function to check if order should be completed from finishing scan
async function checkIfOrderShouldBeCompletedFromFinishing(orderId) {
  const orderWithColis = await Order.findById(orderId).populate({
    path: 'colis',
    populate: {
      path: 'packets'
    }
  });

  if (!orderWithColis) return;

  // Check if all non-blocked packets across all colis are completed
  let allPacketsCompleted = true;
  let finFinitionUser = '';

  // Iterate through all colis and their packets
  for (const colis of orderWithColis.colis) {
    // Update colis status based on its non-blocked packets
    let allColisPacketsCompleted = true;

    for (const packet of colis.packets) {
      // Skip blocked packets - they are ignored in completion logic
      if (packet.bloquer) continue;

      if (packet.status !== 'completed') {
        allPacketsCompleted = false;
        allColisPacketsCompleted = false;
      } else if (!finFinitionUser) {
        // Get the user from the first finFinition scan we find
        finFinitionUser = packet.scans.find(scan => scan.type === 'finFinition')?.user || '';
      }
    }

    // Update colis status if all its packets are completed
    if (allColisPacketsCompleted && colis.status !== 'completed') {
      colis.status = 'completed';
      await colis.save();
    }
  }

  // If all packets across all colis are completed, update order status to "completed"
  if (allPacketsCompleted && orderWithColis.status !== 'completed') {
    orderWithColis.status = 'completed';

    // Add "Sortie Finition" scan when all packets are completed
    orderWithColis.scans.push({
      type: 'SF',
      time: new Date(),
      user: finFinitionUser
    });

    await orderWithColis.save();
    return true;
  }

  return false;
}

// POST /packets/toggle-block
router.post('/toggle-block', async (req, res) => {
  try {
    const { barcode } = req.body;
    const userId = req.user.userId; // Get userId from the authenticated user

    if (!barcode) {
      return res.status(400).json({ message: 'Barcode is required' });
    }

    // Find the packet using the barcode
    const packet = await Packet.findOne({ qrCode: barcode });
    if (!packet) {
      return res.status(404).json({ message: 'Aucun paquet trouvé pour ce code-barres' });
    }

    // Toggle the bloquer status
    packet.bloquer = !packet.bloquer;

    // Add a scan entry to track the block/unblock action
    packet.scans.push({
      type: packet.bloquer ? 'packetBloque' : 'packetDebloque',
      time: new Date(),
      user: userId
    });

    await packet.save();

    const message = packet.bloquer ? 'Packet bloqué' : 'Packet débloqué';

    res.status(200).json({
      message,
      packet
    });

  } catch (err) {
    console.error('Erreur lors du changement de statut du paquet :', err);
    res.status(500).json({ message: 'Échec du changement de statut de blocage du paquet', error: err.message });
  }
});

module.exports = router;
